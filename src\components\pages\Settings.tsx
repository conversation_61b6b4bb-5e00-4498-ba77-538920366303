'use client';

import { useState } from 'react';
import { 
  User, 
  Bell, 
  Shield, 
  Palette, 
  Globe, 
  Database, 
  HelpCircle, 
  Info,
  ChevronRight,
  ArrowLeft
} from 'lucide-react';

interface SettingsProps {
  onBack: () => void;
  currentLanguage: string;
  onLanguageChange: (language: string) => void;
}

const Settings = ({ onBack, currentLanguage, onLanguageChange }: SettingsProps) => {
  const [activeSection, setActiveSection] = useState<string | null>(null);

  const settingsSections = [
    {
      id: 'profile',
      title: 'Profile Settings',
      description: 'Manage your account and personal information',
      icon: User,
      color: 'text-blue-400'
    },
    {
      id: 'notifications',
      title: 'Notifications',
      description: 'Configure alerts and reminders',
      icon: Bell,
      color: 'text-green-400'
    },
    {
      id: 'privacy',
      title: 'Privacy & Security',
      description: 'Control your data and security settings',
      icon: Shield,
      color: 'text-red-400'
    },
    {
      id: 'appearance',
      title: 'Appearance',
      description: 'Customize themes and display options',
      icon: Palette,
      color: 'text-purple-400'
    },
    {
      id: 'language',
      title: 'Language & Region',
      description: 'Set your preferred language and locale',
      icon: Globe,
      color: 'text-yellow-400'
    },
    {
      id: 'data',
      title: 'Data Management',
      description: 'Import, export, and backup your data',
      icon: Database,
      color: 'text-indigo-400'
    },
    {
      id: 'help',
      title: 'Help & Support',
      description: 'Get help and contact support',
      icon: HelpCircle,
      color: 'text-orange-400'
    },
    {
      id: 'about',
      title: 'About Koi App',
      description: 'Version info and legal information',
      icon: Info,
      color: 'text-pink-400'
    }
  ];

  const renderSectionContent = (sectionId: string) => {
    switch (sectionId) {
      case 'profile':
        return (
          <div className="space-y-6">
            <h3 className="text-xl font-bold text-white mb-4">Profile Settings</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-blue-200 text-sm font-medium mb-2">Display Name</label>
                <input 
                  type="text" 
                  className="w-full bg-blue-800/50 text-white rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-400"
                  placeholder="Enter your display name"
                />
              </div>
              <div>
                <label className="block text-blue-200 text-sm font-medium mb-2">Email</label>
                <input 
                  type="email" 
                  className="w-full bg-blue-800/50 text-white rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-400"
                  placeholder="Enter your email"
                />
              </div>
            </div>
          </div>
        );
      case 'notifications':
        return (
          <div className="space-y-6">
            <h3 className="text-xl font-bold text-white mb-4">Notification Settings</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white font-medium">Push Notifications</p>
                  <p className="text-blue-200 text-sm">Receive notifications on your device</p>
                </div>
                <input type="checkbox" className="w-5 h-5" />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white font-medium">Email Notifications</p>
                  <p className="text-blue-200 text-sm">Receive updates via email</p>
                </div>
                <input type="checkbox" className="w-5 h-5" />
              </div>
            </div>
          </div>
        );
      case 'language':
        return (
          <div className="space-y-6">
            <h3 className="text-xl font-bold text-white mb-4">Language & Region</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-blue-200 text-sm font-medium mb-2">Language</label>
                <select 
                  value={currentLanguage}
                  onChange={(e) => onLanguageChange(e.target.value)}
                  className="w-full bg-blue-800/50 text-white rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-400"
                >
                  <option value="en">English (US)</option>
                  <option value="es">Español</option>
                  <option value="fr">Français</option>
                  <option value="de">Deutsch</option>
                  <option value="it">Italiano</option>
                  <option value="pt">Português</option>
                  <option value="nl">Nederlands</option>
                  <option value="zh">中文</option>
                  <option value="ja">日本語</option>
                  <option value="ko">한국어</option>
                  <option value="ru">Русский</option>
                </select>
              </div>
            </div>
          </div>
        );
      default:
        return (
          <div className="space-y-6">
            <h3 className="text-xl font-bold text-white mb-4">Coming Soon</h3>
            <p className="text-blue-200">This settings section is under development.</p>
          </div>
        );
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4 mb-8">
        <button
          onClick={activeSection ? () => setActiveSection(null) : onBack}
          className="p-2 hover:bg-blue-500/30 rounded-lg transition-colors"
        >
          <ArrowLeft className="w-6 h-6 text-white" />
        </button>
        <div>
          <h1 className="text-3xl font-bold text-white">
            {activeSection ? settingsSections.find(s => s.id === activeSection)?.title : 'Settings'}
          </h1>
          <p className="text-blue-200">
            {activeSection 
              ? settingsSections.find(s => s.id === activeSection)?.description 
              : 'Customize your Koi app experience'
            }
          </p>
        </div>
      </div>

      {/* Content */}
      {activeSection ? (
        <div className="bg-gradient-to-br from-blue-500/80 to-blue-600/80 rounded-xl p-6 border border-blue-400/20">
          {renderSectionContent(activeSection)}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {settingsSections.map((section) => (
            <button
              key={section.id}
              onClick={() => setActiveSection(section.id)}
              className="bg-gradient-to-br from-blue-500/80 to-blue-600/80 hover:from-blue-400/80 hover:to-blue-500/80 rounded-xl p-6 text-left transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 border border-blue-400/20"
            >
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-3">
                  <section.icon className={`w-6 h-6 ${section.color}`} />
                  <h3 className="text-lg font-semibold text-white">{section.title}</h3>
                </div>
                <ChevronRight className="w-5 h-5 text-blue-200" />
              </div>
              <p className="text-blue-200 text-sm">{section.description}</p>
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default Settings;
