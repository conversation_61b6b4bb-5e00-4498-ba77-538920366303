"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/pages/Dashboard.tsx":
/*!********************************************!*\
  !*** ./src/components/pages/Dashboard.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sticky-note.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/quote.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/smile.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users-round.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/utensils.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-text.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gamepad-2.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/newspaper.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gift.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/flame.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share.js\");\n/* harmony import */ var _lib_i18n_useTranslation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../lib/i18n/useTranslation */ \"(app-pages-browser)/./src/lib/i18n/useTranslation.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst Dashboard = (param)=>{\n    let { language = 'en' } = param;\n    _s();\n    const { t } = (0,_lib_i18n_useTranslation__WEBPACK_IMPORTED_MODULE_1__.useTranslation)(language);\n    const productivityFeatures = [\n        {\n            id: 'notes',\n            name: t('notes'),\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            description: t('notesDesc')\n        },\n        {\n            id: 'tasks',\n            name: t('tasks'),\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            description: t('tasksDesc')\n        },\n        {\n            id: 'calendar',\n            name: t('calendar'),\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            description: t('calendarDesc')\n        },\n        {\n            id: 'email',\n            name: t('email'),\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            description: t('emailDesc')\n        },\n        {\n            id: 'contacts',\n            name: t('contacts'),\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            description: t('contactsDesc')\n        },\n        {\n            id: 'diary',\n            name: t('diary'),\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            description: t('diaryDesc')\n        },\n        {\n            id: 'health',\n            name: t('health'),\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            description: t('healthDesc')\n        },\n        {\n            id: 'content',\n            name: t('content'),\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            description: t('contentDesc')\n        },\n        {\n            id: 'websites',\n            name: t('websites'),\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            description: t('websitesDesc')\n        }\n    ];\n    const funFeatures = [\n        {\n            id: 'facts',\n            name: 'Fun Facts, Jokes, Quotes & Riddles',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            description: 'Enjoy fun facts, jokes, quotes and riddles',\n            color: 'text-pink-400'\n        },\n        {\n            id: 'excuses',\n            name: 'Excuses',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            description: 'Generate customized excuses',\n            color: 'text-purple-400'\n        },\n        {\n            id: 'companions',\n            name: 'Companions',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            description: 'Manage your virtual companions',\n            color: 'text-pink-400'\n        },\n        {\n            id: 'achievements',\n            name: 'My Achievements',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n            description: 'View your badges, awards and trophies',\n            color: 'text-pink-400'\n        },\n        {\n            id: 'recipes',\n            name: 'My Recipes',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n            description: 'Create and organize your favorite recipes',\n            color: 'text-purple-400'\n        },\n        {\n            id: 'pictures',\n            name: 'My Pictures',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n            description: 'Upload and collect your favorite pictures',\n            color: 'text-pink-400'\n        },\n        {\n            id: 'stories',\n            name: 'Koi Adventure Stories',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n            description: 'Dive into interactive and imaginative Koi stories',\n            color: 'text-pink-400'\n        },\n        {\n            id: 'games',\n            name: 'Mini-Games',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n            description: 'Play quick and fun mini-games to boost your mood',\n            color: 'text-purple-400'\n        },\n        {\n            id: 'news',\n            name: 'Latest News',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n            description: 'Catch up on the latest, family-friendly news',\n            color: 'text-pink-400'\n        }\n    ];\n    const gamingFeatures = [\n        {\n            id: 'level',\n            name: t('levelUp'),\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n            description: t('levelUpDesc')\n        },\n        {\n            id: 'quests',\n            name: t('dailyQuests'),\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n            description: t('dailyQuestsDesc')\n        },\n        {\n            id: 'rewards',\n            name: t('rewards'),\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n            description: t('rewardsDesc')\n        },\n        {\n            id: 'leaderboard',\n            name: t('leaderboard'),\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n            description: t('leaderboardDesc')\n        },\n        {\n            id: 'badges',\n            name: t('badges'),\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n            description: t('badgesDesc')\n        },\n        {\n            id: 'streaks',\n            name: t('streaks'),\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"],\n            description: t('streaksDesc')\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-4xl font-bold text-white mb-4\",\n                        children: \"Welcome back! \\uD83D\\uDC4B\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-blue-200 text-lg\",\n                        children: \"Ready to make today amazing?\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                lineNumber: 79,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl p-6 text-white shadow-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold\",\n                                        children: \"Your Progress\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                        className: \"w-5 h-5 text-blue-200\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-yellow-400 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            className: \"w-5 h-5 text-yellow-900\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xl font-bold\",\n                                                children: \"Level 1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-200 text-sm\",\n                                                children: \"Novice Explorer\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between text-sm mb-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"41/100 XP to next level\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full bg-blue-400 rounded-full h-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-yellow-400 h-2 rounded-full\",\n                                            style: {\n                                                width: '41%'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl p-6 text-white shadow-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold mb-4\",\n                                children: \"How are you feeling?\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-3 gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"bg-blue-400/50 hover:bg-blue-400/70 rounded-lg p-3 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl\",\n                                            children: \"\\uD83D\\uDE0A\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"bg-blue-400/50 hover:bg-blue-400/70 rounded-lg p-3 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl\",\n                                            children: \"\\uD83D\\uDE10\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"bg-blue-400/50 hover:bg-blue-400/70 rounded-lg p-3 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl\",\n                                            children: \"\\uD83D\\uDE14\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"bg-blue-400/50 hover:bg-blue-400/70 rounded-lg p-3 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl\",\n                                            children: \"\\uD83D\\uDE34\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"bg-blue-400/50 hover:bg-blue-400/70 rounded-lg p-3 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl\",\n                                            children: \"\\uD83E\\uDD14\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"bg-blue-400/50 hover:bg-blue-400/70 rounded-lg p-3 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl\",\n                                            children: \"\\uD83D\\uDE0E\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl p-6 text-white shadow-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold mb-4\",\n                                children: \"Daily Inspiration\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-8 h-8 text-blue-200 mx-auto mb-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm italic mb-3\",\n                                        children: '\"The journey of a thousand miles begins with one step.\"'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-blue-200\",\n                                        children: \"- Lao Tzu\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-white mb-2\",\n                                children: \"Productivity Activities\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-200 text-sm\",\n                                children: \"Tools to help you stay organized and efficient\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-3\",\n                        children: productivityFeatures.map((feature)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"bg-gradient-to-br from-blue-500/80 to-blue-600/80 hover:from-blue-400/80 hover:to-blue-500/80 rounded-xl p-4 text-left transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 group border border-blue-400/20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                    className: \"w-5 h-5 text-green-400 drop-shadow-[0_0_8px_rgba(34,197,94,0.8)] group-hover:drop-shadow-[0_0_12px_rgba(34,197,94,1)]\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-base font-semibold text-white\",\n                                                children: feature.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-blue-200 text-xs leading-relaxed\",\n                                        children: feature.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, feature.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                lineNumber: 154,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-white mb-2\",\n                                children: \"Fun Activities\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-200 text-sm\",\n                                children: \"Take a break and have some fun!\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-3\",\n                        children: funFeatures.map((feature)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"bg-gradient-to-br from-blue-500/80 to-blue-600/80 hover:from-blue-400/80 hover:to-blue-500/80 rounded-xl p-4 text-left transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 group border border-blue-400/20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                    className: \"w-5 h-5 text-pink-400 drop-shadow-[0_0_8px_rgba(244,114,182,0.8)] group-hover:drop-shadow-[0_0_12px_rgba(244,114,182,1)]\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                lineNumber: 192,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-base font-semibold text-white\",\n                                                children: feature.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-blue-200 text-xs leading-relaxed\",\n                                        children: feature.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, feature.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                lineNumber: 179,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-white mb-2\",\n                                children: \"Gaming Activities\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-200 text-sm\",\n                                children: \"Level up your life with gamification\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-3\",\n                        children: gamingFeatures.map((feature)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"bg-gradient-to-br from-blue-500/80 to-blue-600/80 hover:from-blue-400/80 hover:to-blue-500/80 rounded-xl p-4 text-left transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 group border border-blue-400/20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                    className: \"w-5 h-5 text-yellow-400 drop-shadow-[0_0_8px_rgba(250,204,21,0.8)] group-hover:drop-shadow-[0_0_12px_rgba(250,204,21,1)]\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-base font-semibold text-white\",\n                                                children: feature.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-blue-200 text-xs leading-relaxed\",\n                                        children: feature.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, feature.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                lineNumber: 204,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-white mb-2\",\n                                children: \"My Daily Progress Visualization\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-200\",\n                                children: \"Watch as your garden grows, sky fills with stars, or river flows as you earn XP and level up!\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-br from-blue-400 via-blue-500 to-blue-600 rounded-2xl p-8 text-white shadow-lg relative overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-4 right-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-16 h-16 bg-yellow-400 rounded-full flex items-center justify-center shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-3xl\",\n                                        children: \"☀️\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-8 left-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-12 h-8 bg-white rounded-full opacity-80 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: \"\\uD83D\\uDE0A\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-12 left-32\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-6 bg-white rounded-full opacity-60\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative z-10 text-center pt-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-block relative\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-48 h-24 bg-gradient-to-b from-orange-400 to-orange-500 rounded-full relative\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-2 w-32 h-16 bg-gradient-to-b from-green-400 to-green-500 rounded-full\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-8 bg-amber-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                            lineNumber: 264,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute -top-2 left-1/2 transform -translate-x-1/2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-2xl\",\n                                                                children: \"\\uD83C\\uDF34\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                                lineNumber: 266,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                            lineNumber: 265,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-lg font-semibold mb-2\",\n                                                children: \"Island\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"max-w-md mx-auto\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-sm mb-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Island Growth: 0%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                            lineNumber: 277,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                        lineNumber: 276,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full bg-blue-300 rounded-full h-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-green-400 h-3 rounded-full\",\n                                                            style: {\n                                                                width: '0%'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                            lineNumber: 280,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                lineNumber: 231,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n        lineNumber: 77,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Dashboard, \"zlIdU9EjM2llFt74AbE2KsUJXyM=\", false, function() {\n    return [\n        _lib_i18n_useTranslation__WEBPACK_IMPORTED_MODULE_1__.useTranslation\n    ];\n});\n_c = Dashboard;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Dashboard);\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/pages/Dashboard.tsx\n"));

/***/ })

});