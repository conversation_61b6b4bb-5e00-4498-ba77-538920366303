import { translations, Language, TranslationKey } from './translations';

export const useTranslation = (language: Language = 'en') => {
  const t = (key: TranslationKey): string => {
    const translation = translations[language]?.[key];
    if (translation) {
      return translation;
    }
    
    // Fallback to English if translation not found
    const fallback = translations.en[key];
    if (fallback) {
      return fallback;
    }
    
    // Return the key if no translation found
    return key;
  };

  return { t };
};

export const getLanguageLabel = (code: string): string => {
  const languageLabels: Record<string, string> = {
    en: 'US English',
    es: 'ES Español',
    fr: 'FR Français',
    de: 'DE Deutsch',
    it: 'IT Italiano',
    pt: 'PT Português',
    nl: 'NL Nederlands',
    zh: 'CN 中文',
    ja: 'JP 日本語',
    ko: 'KR 한국어',
    ru: 'RU Русский',
    gr: 'GR Ελληνικά',
    he: 'IL עברית',
    th: 'TH ไทย',
    vi: 'VN Tiếng Việt',
    id: 'ID Bahasa Indonesia',
  };
  
  return languageLabels[code] || code.toUpperCase();
};
