"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/layout/AppLayout.tsx":
/*!*********************************************!*\
  !*** ./src/components/layout/AppLayout.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _pages_Dashboard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../pages/Dashboard */ \"(app-pages-browser)/./src/components/pages/Dashboard.tsx\");\n/* harmony import */ var _pages_Notes__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../pages/Notes */ \"(app-pages-browser)/./src/components/pages/Notes.tsx\");\n/* harmony import */ var _pages_Settings__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../pages/Settings */ \"(app-pages-browser)/./src/components/pages/Settings.tsx\");\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Header */ \"(app-pages-browser)/./src/components/layout/Header.tsx\");\n/* harmony import */ var _ui_AIAssistantChat__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../ui/AIAssistantChat */ \"(app-pages-browser)/./src/components/ui/AIAssistantChat.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst AppLayout = (param)=>{\n    let { children } = param;\n    _s();\n    const [activeSection, setActiveSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('dashboard');\n    const [showAIChat, setShowAIChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentLanguage, setCurrentLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('en');\n    // Load saved language preference\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppLayout.useEffect\": ()=>{\n            const savedLanguage = localStorage.getItem('koi-app-language');\n            if (savedLanguage) {\n                setCurrentLanguage(savedLanguage);\n            }\n        }\n    }[\"AppLayout.useEffect\"], []);\n    // Save language preference\n    const handleLanguageChange = (language)=>{\n        setCurrentLanguage(language);\n        localStorage.setItem('koi-app-language', language);\n    };\n    const handleAIAssistantClick = ()=>{\n        setShowAIChat(true);\n    };\n    const handleAccountClick = ()=>{\n        // Handle sign out\n        console.log('Sign out clicked');\n    // You can add actual sign out logic here\n    };\n    const handleSettingsClick = ()=>{\n        setActiveSection('settings');\n    };\n    const renderContent = ()=>{\n        switch(activeSection){\n            case 'dashboard':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_pages_Dashboard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    language: currentLanguage\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 16\n                }, undefined);\n            case 'notes':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_pages_Notes__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 16\n                }, undefined);\n            case 'settings':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_pages_Settings__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    onBack: ()=>setActiveSection('dashboard'),\n                    currentLanguage: currentLanguage,\n                    onLanguageChange: handleLanguageChange\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 11\n                }, undefined);\n            case 'tasks':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-white\",\n                        children: \"Tasks - Coming Soon!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 51\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 16\n                }, undefined);\n            case 'content':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-white\",\n                        children: \"Content Creator - Coming Soon!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 51\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 16\n                }, undefined);\n            case 'calendar':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-white\",\n                        children: \"Calendar - Coming Soon!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 51\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-white\",\n                        children: [\n                            activeSection,\n                            \" - Coming Soon!\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 51\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                onAIAssistantClick: handleAIAssistantClick,\n                onAccountClick: handleAccountClick,\n                onSettingsClick: handleSettingsClick,\n                onLanguageChange: handleLanguageChange,\n                currentLanguage: currentLanguage\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: children || renderContent()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_AIAssistantChat__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                isOpen: showAIChat,\n                onClose: ()=>setShowAIChat(false),\n                language: currentLanguage\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AppLayout, \"uXcGXh1QDrNmucpMRGmSdNd8oq4=\");\n_c = AppLayout;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AppLayout);\nvar _c;\n$RefreshReg$(_c, \"AppLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/AppLayout.tsx\n"));

/***/ })

});