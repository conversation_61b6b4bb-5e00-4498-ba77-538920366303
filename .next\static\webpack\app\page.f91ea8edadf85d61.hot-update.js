"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/pages/Dashboard.tsx":
/*!********************************************!*\
  !*** ./src/components/pages/Dashboard.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sticky-note.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/quote.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/smile.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users-round.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/utensils.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-text.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gamepad-2.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/newspaper.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gift.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/flame.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst Dashboard = ()=>{\n    const productivityFeatures = [\n        {\n            id: 'notes',\n            name: 'Notes',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n            description: 'Capture and organize your thoughts'\n        },\n        {\n            id: 'tasks',\n            name: 'Tasks',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            description: 'Manage your daily tasks and to-dos'\n        },\n        {\n            id: 'calendar',\n            name: 'Calendar',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            description: 'Schedule and track your events'\n        },\n        {\n            id: 'email',\n            name: 'Email',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            description: 'Manage all your email accounts in one place'\n        },\n        {\n            id: 'contacts',\n            name: 'Contacts',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            description: 'Keep track of important contacts'\n        },\n        {\n            id: 'diary',\n            name: 'Daily Diary',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            description: 'Record your thoughts and feelings'\n        },\n        {\n            id: 'health',\n            name: 'Healthy Living',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            description: 'Track workouts and nutrition'\n        },\n        {\n            id: 'content',\n            name: 'Content Creator',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            description: 'Create blogs, reports, essays and social media posts'\n        },\n        {\n            id: 'websites',\n            name: 'Favorite Websites',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            description: 'Organize and access your favorite websites'\n        }\n    ];\n    const funFeatures = [\n        {\n            id: 'facts',\n            name: 'Fun Facts, Jokes, Quotes & Riddles',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            description: 'Enjoy fun facts, jokes, quotes and riddles',\n            color: 'text-pink-400'\n        },\n        {\n            id: 'excuses',\n            name: 'Excuses',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            description: 'Generate customized excuses',\n            color: 'text-purple-400'\n        },\n        {\n            id: 'companions',\n            name: 'Companions',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            description: 'Manage your virtual companions',\n            color: 'text-pink-400'\n        },\n        {\n            id: 'achievements',\n            name: 'My Achievements',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            description: 'View your badges, awards and trophies',\n            color: 'text-pink-400'\n        },\n        {\n            id: 'recipes',\n            name: 'My Recipes',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n            description: 'Create and organize your favorite recipes',\n            color: 'text-purple-400'\n        },\n        {\n            id: 'pictures',\n            name: 'My Pictures',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n            description: 'Upload and collect your favorite pictures',\n            color: 'text-pink-400'\n        },\n        {\n            id: 'stories',\n            name: 'Koi Adventure Stories',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n            description: 'Dive into interactive and imaginative Koi stories',\n            color: 'text-pink-400'\n        },\n        {\n            id: 'games',\n            name: 'Mini-Games',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n            description: 'Play quick and fun mini-games to boost your mood',\n            color: 'text-purple-400'\n        },\n        {\n            id: 'news',\n            name: 'Latest News',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n            description: 'Catch up on the latest, family-friendly news',\n            color: 'text-pink-400'\n        }\n    ];\n    const gamingFeatures = [\n        {\n            id: 'level',\n            name: 'Level Up',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n            description: 'Track your progress and level up'\n        },\n        {\n            id: 'quests',\n            name: 'Daily Quests',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n            description: 'Complete challenges and earn rewards'\n        },\n        {\n            id: 'rewards',\n            name: 'Rewards',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n            description: 'Unlock achievements and collect rewards'\n        },\n        {\n            id: 'leaderboard',\n            name: 'Leaderboard',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            description: 'Compete with friends and climb ranks'\n        },\n        {\n            id: 'badges',\n            name: 'Badges',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n            description: 'Earn badges for your accomplishments'\n        },\n        {\n            id: 'streaks',\n            name: 'Streaks',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n            description: 'Maintain daily activity streaks'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-4xl font-bold text-white mb-4\",\n                        children: \"Welcome back! \\uD83D\\uDC4B\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-blue-200 text-lg\",\n                        children: \"Ready to make today amazing?\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                lineNumber: 72,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl p-6 text-white shadow-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold\",\n                                        children: \"Your Progress\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                        className: \"w-5 h-5 text-blue-200\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-yellow-400 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"w-5 h-5 text-yellow-900\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xl font-bold\",\n                                                children: \"Level 1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                lineNumber: 94,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-200 text-sm\",\n                                                children: \"Novice Explorer\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between text-sm mb-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"41/100 XP to next level\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full bg-blue-400 rounded-full h-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-yellow-400 h-2 rounded-full\",\n                                            style: {\n                                                width: '41%'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl p-6 text-white shadow-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold mb-4\",\n                                children: \"How are you feeling?\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-3 gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"bg-blue-400/50 hover:bg-blue-400/70 rounded-lg p-3 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl\",\n                                            children: \"\\uD83D\\uDE0A\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"bg-blue-400/50 hover:bg-blue-400/70 rounded-lg p-3 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl\",\n                                            children: \"\\uD83D\\uDE10\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"bg-blue-400/50 hover:bg-blue-400/70 rounded-lg p-3 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl\",\n                                            children: \"\\uD83D\\uDE14\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"bg-blue-400/50 hover:bg-blue-400/70 rounded-lg p-3 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl\",\n                                            children: \"\\uD83D\\uDE34\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"bg-blue-400/50 hover:bg-blue-400/70 rounded-lg p-3 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl\",\n                                            children: \"\\uD83E\\uDD14\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"bg-blue-400/50 hover:bg-blue-400/70 rounded-lg p-3 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl\",\n                                            children: \"\\uD83D\\uDE0E\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl p-6 text-white shadow-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold mb-4\",\n                                children: \"Daily Inspiration\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-8 h-8 text-blue-200 mx-auto mb-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm italic mb-3\",\n                                        children: '\"The journey of a thousand miles begins with one step.\"'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-blue-200\",\n                                        children: \"- Lao Tzu\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-white mb-2\",\n                                children: \"Productivity Activities\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-200 text-sm\",\n                                children: \"Tools to help you stay organized and efficient\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-3\",\n                        children: productivityFeatures.map((feature)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"bg-gradient-to-br from-blue-500/80 to-blue-600/80 hover:from-blue-400/80 hover:to-blue-500/80 rounded-xl p-4 text-left transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 group border border-blue-400/20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                    className: \"w-5 h-5 text-green-400 drop-shadow-[0_0_8px_rgba(34,197,94,0.8)] group-hover:drop-shadow-[0_0_12px_rgba(34,197,94,1)]\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                    lineNumber: 161,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-base font-semibold text-white\",\n                                                children: feature.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-blue-200 text-xs leading-relaxed\",\n                                        children: feature.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, feature.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                lineNumber: 147,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-white mb-2\",\n                                children: \"Fun Activities\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-200 text-sm\",\n                                children: \"Take a break and have some fun!\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-3\",\n                        children: funFeatures.map((feature)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"bg-gradient-to-br from-blue-500/80 to-blue-600/80 hover:from-blue-400/80 hover:to-blue-500/80 rounded-xl p-4 text-left transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 group border border-blue-400/20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                    className: \"w-5 h-5 text-pink-400 drop-shadow-[0_0_8px_rgba(244,114,182,0.8)] group-hover:drop-shadow-[0_0_12px_rgba(244,114,182,1)]\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-base font-semibold text-white\",\n                                                children: feature.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-blue-200 text-xs leading-relaxed\",\n                                        children: feature.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, feature.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                lineNumber: 172,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-white mb-2\",\n                                children: \"Gaming Activities\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-200 text-sm\",\n                                children: \"Level up your life with gamification\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-3\",\n                        children: gamingFeatures.map((feature)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"bg-gradient-to-br from-blue-500/80 to-blue-600/80 hover:from-blue-400/80 hover:to-blue-500/80 rounded-xl p-4 text-left transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 group border border-blue-400/20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                    className: \"w-5 h-5 text-yellow-400 drop-shadow-[0_0_8px_rgba(250,204,21,0.8)] group-hover:drop-shadow-[0_0_12px_rgba(250,204,21,1)]\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-base font-semibold text-white\",\n                                                children: feature.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-blue-200 text-xs leading-relaxed\",\n                                        children: feature.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, feature.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                lineNumber: 197,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-white mb-2\",\n                                children: \"My Daily Progress Visualization\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-200\",\n                                children: \"Watch as your garden grows, sky fills with stars, or river flows as you earn XP and level up!\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-br from-blue-400 via-blue-500 to-blue-600 rounded-2xl p-8 text-white shadow-lg relative overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-4 right-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-16 h-16 bg-yellow-400 rounded-full flex items-center justify-center shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-3xl\",\n                                        children: \"☀️\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                    lineNumber: 233,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-8 left-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-12 h-8 bg-white rounded-full opacity-80 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: \"\\uD83D\\uDE0A\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-12 left-32\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-6 bg-white rounded-full opacity-60\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative z-10 text-center pt-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-block relative\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-48 h-24 bg-gradient-to-b from-orange-400 to-orange-500 rounded-full relative\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-2 w-32 h-16 bg-gradient-to-b from-green-400 to-green-500 rounded-full\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-8 bg-amber-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                            lineNumber: 257,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute -top-2 left-1/2 transform -translate-x-1/2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-2xl\",\n                                                                children: \"\\uD83C\\uDF34\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                                lineNumber: 259,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                            lineNumber: 258,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-lg font-semibold mb-2\",\n                                                children: \"Island\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"max-w-md mx-auto\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-sm mb-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Island Growth: 0%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                            lineNumber: 270,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full bg-blue-300 rounded-full h-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-green-400 h-3 rounded-full\",\n                                                            style: {\n                                                                width: '0%'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                            lineNumber: 273,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                        lineNumber: 272,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                lineNumber: 224,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n        lineNumber: 70,\n        columnNumber: 5\n    }, undefined);\n};\n_c = Dashboard;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Dashboard);\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/pages/Dashboard.tsx\n"));

/***/ })

});