# Koi Joyful Habits Hub - Supabase Configuration Guide

## 🚨 CRITICAL SECURITY NOTICE
This is an enterprise-grade multi-tenant SaaS application requiring strict security measures for GDPR/CCPA compliance and data protection.

## Prerequisites
1. Supabase account with a project created
2. Google Cloud Console account for OAuth setup
3. Domain configured for production deployment

## 1. Database Setup

### Apply Migrations
```bash
# Navigate to your project directory
cd koi-app3

# Apply the database schema
supabase db push

# Or manually run the SQL files in Supabase Dashboard > SQL Editor:
# 1. Run supabase/migrations/001_initial_schema.sql
# 2. Run supabase/migrations/002_functions_and_triggers.sql
```

### Verify Security
After applying migrations, verify that:
- ✅ All user data tables have Row Level Security (RLS) enabled
- ✅ All RLS policies are correctly applied
- ✅ No user can access another user's data
- ✅ Activity logging is working for audit trails

## 2. Authentication Configuration

### Email Authentication
1. Go to Supabase Dashboard > Authentication > Settings
2. Enable "Enable email confirmations" for security
3. Set "Site URL" to your domain (production) or `http://localhost:3000` (development)
4. Configure email templates for your brand

### Magic Link Setup
1. In Authentication > Settings
2. Enable "Enable email confirmations"
3. Set redirect URLs:
   - Development: `http://localhost:3000/auth/callback`
   - Production: `https://yourdomain.com/auth/callback`

### Google OAuth Setup

#### Step 1: Google Cloud Console
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable Google+ API and Google Identity API
4. Go to "Credentials" > "Create Credentials" > "OAuth 2.0 Client IDs"
5. Set application type to "Web application"
6. Add authorized redirect URIs:
   ```
   https://[your-supabase-project].supabase.co/auth/v1/callback
   ```
7. Copy the Client ID and Client Secret

#### Step 2: Supabase Configuration
1. Go to Supabase Dashboard > Authentication > Providers
2. Enable Google provider
3. Enter your Google Client ID and Client Secret
4. Set redirect URL to: `https://[your-project].supabase.co/auth/v1/callback`

#### Step 3: Test OAuth Flow
1. Test sign-in with Google on your auth page
2. Verify user profile creation
3. Check that RLS policies work correctly

## 3. Environment Variables

Update your `.env.local` file:
```env
# Supabase Configuration (already configured)
NEXT_PUBLIC_SUPABASE_URL=https://arkywiwduoqpobflwssm.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here

# Google OAuth (add these)
GOOGLE_CLIENT_ID=your_google_client_id_here
GOOGLE_CLIENT_SECRET=your_google_client_secret_here

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

## 4. Security Verification Checklist

### ✅ Row Level Security (RLS)
- [ ] All user data tables have RLS enabled
- [ ] Users can only access their own data
- [ ] Test with multiple user accounts
- [ ] Verify no data leakage between users

### ✅ Authentication Security
- [ ] Email verification required for new accounts
- [ ] Password strength requirements enforced
- [ ] OAuth providers properly configured
- [ ] Session management working correctly

### ✅ Data Privacy (GDPR/CCPA)
- [ ] Activity logging implemented
- [ ] Data export functionality available
- [ ] Data deletion requests supported
- [ ] User consent mechanisms in place

### ✅ API Security
- [ ] All API endpoints protected
- [ ] Rate limiting configured
- [ ] Input validation implemented
- [ ] SQL injection prevention verified

## 5. Production Deployment

### Domain Configuration
1. Set up your custom domain
2. Configure SSL certificates
3. Update redirect URLs in:
   - Supabase Authentication settings
   - Google OAuth configuration
   - Environment variables

### Security Headers
Add these security headers to your deployment:
```
Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline';
X-Frame-Options: DENY
X-Content-Type-Options: nosniff
Referrer-Policy: strict-origin-when-cross-origin
Permissions-Policy: geolocation=(), microphone=(), camera=()
```

### Monitoring & Alerts
1. Set up Supabase monitoring
2. Configure error tracking (Sentry recommended)
3. Set up uptime monitoring
4. Configure security alerts

## 6. Testing Authentication

### Test Scenarios
1. **Email/Password Sign Up**
   - Create account with email/password
   - Verify email confirmation required
   - Test sign-in after confirmation

2. **Magic Link Authentication**
   - Request magic link
   - Check email delivery
   - Test link expiration

3. **Google OAuth**
   - Test Google sign-in flow
   - Verify profile data import
   - Test account linking

4. **Security Testing**
   - Test with multiple users
   - Verify data isolation
   - Test session management
   - Verify logout functionality

## 7. Troubleshooting

### Common Issues

#### OAuth Redirect Mismatch
```
Error: redirect_uri_mismatch
Solution: Ensure redirect URIs match exactly in Google Console and Supabase
```

#### RLS Policy Errors
```
Error: new row violates row-level security policy
Solution: Check that user_id is properly set and matches auth.uid()
```

#### Email Delivery Issues
```
Issue: Confirmation emails not received
Solution: Check Supabase email settings and spam folders
```

### Debug Commands
```sql
-- Check RLS status
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE schemaname = 'public';

-- Test user data isolation
SELECT * FROM notes WHERE user_id != auth.uid(); -- Should return empty

-- Check activity logs
SELECT * FROM activity_logs WHERE user_id = auth.uid() ORDER BY created_at DESC;
```

## 8. Compliance & Legal

### GDPR Compliance
- ✅ User consent mechanisms
- ✅ Data portability (export)
- ✅ Right to erasure (deletion)
- ✅ Activity logging for audits
- ✅ Data minimization principles

### Security Best Practices
- ✅ Encryption at rest and in transit
- ✅ Regular security audits
- ✅ Access logging and monitoring
- ✅ Incident response procedures
- ✅ Data backup and recovery

## 9. Support & Maintenance

### Regular Tasks
- Monitor user activity and performance
- Review security logs weekly
- Update dependencies monthly
- Backup database regularly
- Test disaster recovery procedures

### Emergency Contacts
- Supabase Support: [<EMAIL>](mailto:<EMAIL>)
- Security Issues: Immediate escalation required
- Data Breaches: Follow incident response plan

---

## ⚠️ CRITICAL REMINDER
This application handles sensitive user data. Any security vulnerability could result in:
- Financial losses
- Legal liability
- Loss of user trust
- Regulatory penalties

Always prioritize security and compliance in all development and deployment decisions.
