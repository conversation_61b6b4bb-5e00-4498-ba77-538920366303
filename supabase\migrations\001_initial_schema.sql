-- =====================================================
-- KOI JOYFUL HABITS HUB - COMPREHENSIVE DATABASE SCHEMA
-- Enterprise-Grade Multi-Tenant SaaS Application
-- GDPR/CCPA Compliant with Row Level Security (RLS)
-- =====================================================

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- =====================================================
-- CORE USER PROFILE TABLE
-- =====================================================
CREATE TABLE user_profiles (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    email TEXT UNIQUE NOT NULL,
    full_name TEXT,
    avatar_url TEXT,
    language_preference TEXT DEFAULT 'en',
    timezone TEXT DEFAULT 'UTC',
    theme_preference TEXT DEFAULT 'default',
    notification_preferences JSONB DEFAULT '{}',
    privacy_settings JSONB DEFAULT '{}',
    subscription_tier TEXT DEFAULT 'free',
    subscription_status TEXT DEFAULT 'active',
    trial_ends_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    last_active_at TIMESTAMPTZ DEFAULT NOW(),
    is_active BOOLEAN DEFAULT true,
    metadata JSONB DEFAULT '{}'
);

-- =====================================================
-- PRODUCTIVITY FEATURES TABLES
-- =====================================================

-- Notes Management
CREATE TABLE notes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    content TEXT,
    content_type TEXT DEFAULT 'text', -- text, markdown, rich_text
    folder_id UUID REFERENCES folders(id) ON DELETE SET NULL,
    tags TEXT[],
    is_favorite BOOLEAN DEFAULT false,
    is_archived BOOLEAN DEFAULT false,
    is_deleted BOOLEAN DEFAULT false,
    sharing_settings JSONB DEFAULT '{}',
    encryption_key TEXT, -- For end-to-end encryption
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    deleted_at TIMESTAMPTZ
);

-- Tasks Management
CREATE TABLE tasks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    description TEXT,
    status TEXT DEFAULT 'pending', -- pending, in_progress, completed, cancelled
    priority TEXT DEFAULT 'medium', -- low, medium, high, urgent
    due_date TIMESTAMPTZ,
    reminder_at TIMESTAMPTZ,
    category_id UUID REFERENCES categories(id) ON DELETE SET NULL,
    project_id UUID REFERENCES projects(id) ON DELETE SET NULL,
    parent_task_id UUID REFERENCES tasks(id) ON DELETE CASCADE,
    estimated_duration INTEGER, -- in minutes
    actual_duration INTEGER, -- in minutes
    completion_percentage INTEGER DEFAULT 0,
    tags TEXT[],
    attachments JSONB DEFAULT '[]',
    is_recurring BOOLEAN DEFAULT false,
    recurrence_pattern JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    completed_at TIMESTAMPTZ
);

-- Calendar Events
CREATE TABLE calendar_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    description TEXT,
    start_time TIMESTAMPTZ NOT NULL,
    end_time TIMESTAMPTZ NOT NULL,
    all_day BOOLEAN DEFAULT false,
    location TEXT,
    event_type TEXT DEFAULT 'personal', -- personal, work, meeting, reminder
    calendar_id UUID REFERENCES calendars(id) ON DELETE CASCADE,
    attendees JSONB DEFAULT '[]',
    reminder_settings JSONB DEFAULT '[]',
    recurrence_rule TEXT,
    is_private BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Email Management
CREATE TABLE email_accounts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    email_address TEXT NOT NULL,
    provider TEXT NOT NULL, -- gmail, outlook, yahoo, custom
    display_name TEXT,
    is_primary BOOLEAN DEFAULT false,
    sync_enabled BOOLEAN DEFAULT true,
    last_sync_at TIMESTAMPTZ,
    settings JSONB DEFAULT '{}',
    encrypted_credentials TEXT, -- Encrypted OAuth tokens
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Contacts Management
CREATE TABLE contacts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    first_name TEXT,
    last_name TEXT,
    email_addresses JSONB DEFAULT '[]',
    phone_numbers JSONB DEFAULT '[]',
    company TEXT,
    job_title TEXT,
    address JSONB,
    birthday DATE,
    notes TEXT,
    tags TEXT[],
    avatar_url TEXT,
    social_links JSONB DEFAULT '{}',
    custom_fields JSONB DEFAULT '{}',
    is_favorite BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Daily Diary
CREATE TABLE diary_entries (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    entry_date DATE NOT NULL,
    title TEXT,
    content TEXT NOT NULL,
    mood_rating INTEGER CHECK (mood_rating >= 1 AND mood_rating <= 10),
    weather TEXT,
    activities TEXT[],
    gratitude_items TEXT[],
    goals_progress JSONB DEFAULT '{}',
    photos TEXT[],
    is_private BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id, entry_date)
);

-- Health & Wellness
CREATE TABLE health_records (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    record_type TEXT NOT NULL, -- workout, nutrition, sleep, vitals, medication
    record_date DATE NOT NULL,
    data JSONB NOT NULL,
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Content Creation
CREATE TABLE content_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    content_type TEXT NOT NULL, -- blog, report, essay, social_post, script
    content TEXT NOT NULL,
    status TEXT DEFAULT 'draft', -- draft, published, archived
    platform TEXT, -- for social media posts
    scheduled_for TIMESTAMPTZ,
    published_at TIMESTAMPTZ,
    metadata JSONB DEFAULT '{}',
    tags TEXT[],
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Favorite Websites
CREATE TABLE favorite_websites (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    url TEXT NOT NULL,
    description TEXT,
    category TEXT,
    favicon_url TEXT,
    tags TEXT[],
    visit_count INTEGER DEFAULT 0,
    last_visited_at TIMESTAMPTZ,
    is_favorite BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- GAMING & GAMIFICATION TABLES
-- =====================================================

-- User Progress & Levels
CREATE TABLE user_progress (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    current_level INTEGER DEFAULT 1,
    total_xp INTEGER DEFAULT 0,
    xp_to_next_level INTEGER DEFAULT 100,
    daily_streak INTEGER DEFAULT 0,
    longest_streak INTEGER DEFAULT 0,
    last_activity_date DATE,
    achievements_unlocked INTEGER DEFAULT 0,
    badges_earned INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id)
);

-- Achievements System
CREATE TABLE achievements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL UNIQUE,
    description TEXT NOT NULL,
    icon TEXT,
    category TEXT NOT NULL,
    xp_reward INTEGER DEFAULT 0,
    rarity TEXT DEFAULT 'common', -- common, rare, epic, legendary
    requirements JSONB NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- User Achievements (Many-to-Many)
CREATE TABLE user_achievements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    achievement_id UUID NOT NULL REFERENCES achievements(id) ON DELETE CASCADE,
    earned_at TIMESTAMPTZ DEFAULT NOW(),
    progress JSONB DEFAULT '{}',
    UNIQUE(user_id, achievement_id)
);

-- Quests System
CREATE TABLE quests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    quest_type TEXT NOT NULL, -- daily, weekly, monthly, special
    category TEXT NOT NULL,
    requirements JSONB NOT NULL,
    xp_reward INTEGER DEFAULT 0,
    badge_reward UUID REFERENCES badges(id),
    start_date DATE,
    end_date DATE,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- User Quest Progress
CREATE TABLE user_quest_progress (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    quest_id UUID NOT NULL REFERENCES quests(id) ON DELETE CASCADE,
    status TEXT DEFAULT 'active', -- active, completed, failed, abandoned
    progress JSONB DEFAULT '{}',
    started_at TIMESTAMPTZ DEFAULT NOW(),
    completed_at TIMESTAMPTZ,
    UNIQUE(user_id, quest_id)
);

-- Badges System
CREATE TABLE badges (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL UNIQUE,
    description TEXT NOT NULL,
    icon TEXT,
    category TEXT NOT NULL,
    rarity TEXT DEFAULT 'common',
    requirements JSONB,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- User Badges
CREATE TABLE user_badges (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    badge_id UUID NOT NULL REFERENCES badges(id) ON DELETE CASCADE,
    earned_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id, badge_id)
);

-- Leaderboards
CREATE TABLE leaderboard_entries (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    leaderboard_type TEXT NOT NULL, -- xp, streak, achievements, etc.
    score INTEGER NOT NULL,
    rank INTEGER,
    period TEXT NOT NULL, -- daily, weekly, monthly, all_time
    period_start DATE NOT NULL,
    period_end DATE NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id, leaderboard_type, period, period_start)
);

-- =====================================================
-- SUPPORTING TABLES
-- =====================================================

-- Folders for organization
CREATE TABLE folders (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    parent_folder_id UUID REFERENCES folders(id) ON DELETE CASCADE,
    color TEXT,
    icon TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Categories for tasks and content
CREATE TABLE categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    color TEXT,
    icon TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Projects for task organization
CREATE TABLE projects (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    status TEXT DEFAULT 'active',
    start_date DATE,
    end_date DATE,
    color TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Calendars for event organization
CREATE TABLE calendars (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    color TEXT,
    is_default BOOLEAN DEFAULT false,
    is_visible BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- AUDIT & COMPLIANCE TABLES
-- =====================================================

-- Activity Logs for compliance and debugging
CREATE TABLE activity_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    action TEXT NOT NULL,
    resource_type TEXT NOT NULL,
    resource_id UUID,
    details JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Data Export Requests (GDPR Compliance)
CREATE TABLE data_export_requests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    status TEXT DEFAULT 'pending', -- pending, processing, completed, failed
    export_type TEXT NOT NULL, -- full, partial
    file_url TEXT,
    expires_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    completed_at TIMESTAMPTZ
);

-- Data Deletion Requests (GDPR Compliance)
CREATE TABLE data_deletion_requests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    status TEXT DEFAULT 'pending', -- pending, processing, completed
    deletion_type TEXT NOT NULL, -- account, specific_data
    data_types TEXT[],
    scheduled_for TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    completed_at TIMESTAMPTZ
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- User-specific indexes for multi-tenant queries
CREATE INDEX idx_notes_user_id ON notes(user_id);
CREATE INDEX idx_tasks_user_id ON tasks(user_id);
CREATE INDEX idx_calendar_events_user_id ON calendar_events(user_id);
CREATE INDEX idx_contacts_user_id ON contacts(user_id);
CREATE INDEX idx_diary_entries_user_id ON diary_entries(user_id);
CREATE INDEX idx_health_records_user_id ON health_records(user_id);
CREATE INDEX idx_content_items_user_id ON content_items(user_id);
CREATE INDEX idx_favorite_websites_user_id ON favorite_websites(user_id);

-- Performance indexes
CREATE INDEX idx_tasks_status ON tasks(status);
CREATE INDEX idx_tasks_due_date ON tasks(due_date);
CREATE INDEX idx_calendar_events_start_time ON calendar_events(start_time);
CREATE INDEX idx_diary_entries_date ON diary_entries(entry_date);
CREATE INDEX idx_activity_logs_created_at ON activity_logs(created_at);

-- =====================================================
-- UPDATED_AT TRIGGERS
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers to all tables with updated_at
CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE ON user_profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_notes_updated_at BEFORE UPDATE ON notes FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_tasks_updated_at BEFORE UPDATE ON tasks FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_calendar_events_updated_at BEFORE UPDATE ON calendar_events FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_contacts_updated_at BEFORE UPDATE ON contacts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_diary_entries_updated_at BEFORE UPDATE ON diary_entries FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_health_records_updated_at BEFORE UPDATE ON health_records FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_content_items_updated_at BEFORE UPDATE ON content_items FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_progress_updated_at BEFORE UPDATE ON user_progress FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- CRITICAL FOR MULTI-TENANT SECURITY & GDPR COMPLIANCE
-- =====================================================

-- Enable RLS on all user data tables
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE notes ENABLE ROW LEVEL SECURITY;
ALTER TABLE tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE calendar_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE email_accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE contacts ENABLE ROW LEVEL SECURITY;
ALTER TABLE diary_entries ENABLE ROW LEVEL SECURITY;
ALTER TABLE health_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE content_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE favorite_websites ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_quest_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_badges ENABLE ROW LEVEL SECURITY;
ALTER TABLE leaderboard_entries ENABLE ROW LEVEL SECURITY;
ALTER TABLE folders ENABLE ROW LEVEL SECURITY;
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE calendars ENABLE ROW LEVEL SECURITY;
ALTER TABLE activity_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE data_export_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE data_deletion_requests ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- USER PROFILE POLICIES
-- =====================================================
CREATE POLICY "Users can view own profile" ON user_profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON user_profiles
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON user_profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

-- =====================================================
-- NOTES POLICIES
-- =====================================================
CREATE POLICY "Users can view own notes" ON notes
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own notes" ON notes
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own notes" ON notes
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own notes" ON notes
    FOR DELETE USING (auth.uid() = user_id);

-- =====================================================
-- TASKS POLICIES
-- =====================================================
CREATE POLICY "Users can view own tasks" ON tasks
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own tasks" ON tasks
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own tasks" ON tasks
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own tasks" ON tasks
    FOR DELETE USING (auth.uid() = user_id);

-- =====================================================
-- CALENDAR EVENTS POLICIES
-- =====================================================
CREATE POLICY "Users can view own calendar events" ON calendar_events
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own calendar events" ON calendar_events
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own calendar events" ON calendar_events
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own calendar events" ON calendar_events
    FOR DELETE USING (auth.uid() = user_id);

-- =====================================================
-- EMAIL ACCOUNTS POLICIES
-- =====================================================
CREATE POLICY "Users can view own email accounts" ON email_accounts
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own email accounts" ON email_accounts
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own email accounts" ON email_accounts
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own email accounts" ON email_accounts
    FOR DELETE USING (auth.uid() = user_id);

-- =====================================================
-- CONTACTS POLICIES
-- =====================================================
CREATE POLICY "Users can view own contacts" ON contacts
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own contacts" ON contacts
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own contacts" ON contacts
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own contacts" ON contacts
    FOR DELETE USING (auth.uid() = user_id);

-- =====================================================
-- DIARY ENTRIES POLICIES
-- =====================================================
CREATE POLICY "Users can view own diary entries" ON diary_entries
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own diary entries" ON diary_entries
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own diary entries" ON diary_entries
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own diary entries" ON diary_entries
    FOR DELETE USING (auth.uid() = user_id);

-- =====================================================
-- HEALTH RECORDS POLICIES
-- =====================================================
CREATE POLICY "Users can view own health records" ON health_records
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own health records" ON health_records
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own health records" ON health_records
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own health records" ON health_records
    FOR DELETE USING (auth.uid() = user_id);

-- =====================================================
-- CONTENT ITEMS POLICIES
-- =====================================================
CREATE POLICY "Users can view own content items" ON content_items
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own content items" ON content_items
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own content items" ON content_items
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own content items" ON content_items
    FOR DELETE USING (auth.uid() = user_id);

-- =====================================================
-- FAVORITE WEBSITES POLICIES
-- =====================================================
CREATE POLICY "Users can view own favorite websites" ON favorite_websites
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own favorite websites" ON favorite_websites
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own favorite websites" ON favorite_websites
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own favorite websites" ON favorite_websites
    FOR DELETE USING (auth.uid() = user_id);

-- =====================================================
-- GAMING & GAMIFICATION POLICIES
-- =====================================================

-- User Progress Policies
CREATE POLICY "Users can view own progress" ON user_progress
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own progress" ON user_progress
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own progress" ON user_progress
    FOR UPDATE USING (auth.uid() = user_id);

-- User Achievements Policies
CREATE POLICY "Users can view own achievements" ON user_achievements
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own achievements" ON user_achievements
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- User Quest Progress Policies
CREATE POLICY "Users can view own quest progress" ON user_quest_progress
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own quest progress" ON user_quest_progress
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own quest progress" ON user_quest_progress
    FOR UPDATE USING (auth.uid() = user_id);

-- User Badges Policies
CREATE POLICY "Users can view own badges" ON user_badges
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own badges" ON user_badges
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Leaderboard Policies (users can view all, but only insert/update their own)
CREATE POLICY "Users can view leaderboard entries" ON leaderboard_entries
    FOR SELECT USING (true); -- Public leaderboard viewing

CREATE POLICY "Users can insert own leaderboard entries" ON leaderboard_entries
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own leaderboard entries" ON leaderboard_entries
    FOR UPDATE USING (auth.uid() = user_id);

-- =====================================================
-- SUPPORTING TABLES POLICIES
-- =====================================================

-- Folders Policies
CREATE POLICY "Users can view own folders" ON folders
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own folders" ON folders
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own folders" ON folders
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own folders" ON folders
    FOR DELETE USING (auth.uid() = user_id);

-- Categories Policies
CREATE POLICY "Users can view own categories" ON categories
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own categories" ON categories
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own categories" ON categories
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own categories" ON categories
    FOR DELETE USING (auth.uid() = user_id);

-- Projects Policies
CREATE POLICY "Users can view own projects" ON projects
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own projects" ON projects
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own projects" ON projects
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own projects" ON projects
    FOR DELETE USING (auth.uid() = user_id);

-- Calendars Policies
CREATE POLICY "Users can view own calendars" ON calendars
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own calendars" ON calendars
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own calendars" ON calendars
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own calendars" ON calendars
    FOR DELETE USING (auth.uid() = user_id);

-- =====================================================
-- AUDIT & COMPLIANCE POLICIES
-- =====================================================

-- Activity Logs (users can only view their own)
CREATE POLICY "Users can view own activity logs" ON activity_logs
    FOR SELECT USING (auth.uid() = user_id);

-- Data Export Requests
CREATE POLICY "Users can view own export requests" ON data_export_requests
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own export requests" ON data_export_requests
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Data Deletion Requests
CREATE POLICY "Users can view own deletion requests" ON data_deletion_requests
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own deletion requests" ON data_deletion_requests
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- =====================================================
-- PUBLIC TABLES (No RLS needed)
-- =====================================================
-- achievements, quests, badges are public reference data
-- Users can read but not modify

CREATE POLICY "Anyone can view achievements" ON achievements
    FOR SELECT USING (true);

CREATE POLICY "Anyone can view quests" ON quests
    FOR SELECT USING (true);

CREATE POLICY "Anyone can view badges" ON badges
    FOR SELECT USING (true);

-- =====================================================
-- SECURITY FUNCTIONS
-- =====================================================

-- Function to ensure user can only access their own data
CREATE OR REPLACE FUNCTION auth.user_id() RETURNS UUID AS $$
  SELECT auth.uid();
$$ LANGUAGE SQL STABLE;

-- Function to log user activity (GDPR compliance)
CREATE OR REPLACE FUNCTION log_user_activity(
    action_type TEXT,
    resource_type TEXT,
    resource_id UUID DEFAULT NULL,
    details JSONB DEFAULT '{}'
) RETURNS VOID AS $$
BEGIN
    INSERT INTO activity_logs (user_id, action, resource_type, resource_id, details, ip_address)
    VALUES (
        auth.uid(),
        action_type,
        resource_type,
        resource_id,
        details,
        inet_client_addr()
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- INITIAL DATA SETUP
-- =====================================================

-- Insert default achievements
INSERT INTO achievements (name, description, category, xp_reward, requirements) VALUES
('First Steps', 'Complete your first task', 'productivity', 10, '{"tasks_completed": 1}'),
('Task Master', 'Complete 10 tasks', 'productivity', 50, '{"tasks_completed": 10}'),
('Productivity Pro', 'Complete 100 tasks', 'productivity', 200, '{"tasks_completed": 100}'),
('Daily Warrior', 'Maintain a 7-day streak', 'consistency', 100, '{"daily_streak": 7}'),
('Streak Legend', 'Maintain a 30-day streak', 'consistency', 500, '{"daily_streak": 30}'),
('Note Taker', 'Create your first note', 'productivity', 5, '{"notes_created": 1}'),
('Diary Keeper', 'Write 7 diary entries', 'wellness', 75, '{"diary_entries": 7}'),
('Health Conscious', 'Log 10 health records', 'wellness', 100, '{"health_records": 10}'),
('Social Butterfly', 'Add 25 contacts', 'social', 50, '{"contacts_added": 25}'),
('Content Creator', 'Create 5 content items', 'creativity', 100, '{"content_items": 5}');

-- Insert default badges
INSERT INTO badges (name, description, category, requirements) VALUES
('Early Adopter', 'One of the first users of Koi App', 'special', '{"signup_date": "2024-01-01"}'),
('Completionist', 'Complete all available achievements', 'achievement', '{"all_achievements": true}'),
('Consistency King', 'Maintain longest streak record', 'consistency', '{"longest_streak": 100}'),
('Power User', 'Use all app features', 'engagement', '{"features_used": 18}'),
('Community Helper', 'Help other users', 'social', '{"help_actions": 10}');

-- Insert default quests
INSERT INTO quests (title, description, quest_type, category, requirements, xp_reward) VALUES
('Daily Check-in', 'Log in to the app', 'daily', 'engagement', '{"login": true}', 5),
('Task Completion', 'Complete 3 tasks today', 'daily', 'productivity', '{"tasks_completed": 3}', 15),
('Note Taking', 'Create 2 notes today', 'daily', 'productivity', '{"notes_created": 2}', 10),
('Weekly Warrior', 'Complete 15 tasks this week', 'weekly', 'productivity', '{"tasks_completed": 15}', 100),
('Monthly Master', 'Complete 50 tasks this month', 'monthly', 'productivity', '{"tasks_completed": 50}', 500),
('Diary Writer', 'Write in diary for 7 consecutive days', 'weekly', 'wellness', '{"diary_streak": 7}', 150),
('Health Tracker', 'Log health data for 5 days', 'weekly', 'wellness', '{"health_logs": 5}', 75);

-- =====================================================
-- PERFORMANCE OPTIMIZATIONS
-- =====================================================

-- Additional indexes for common queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_profiles_email ON user_profiles(email);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_tasks_user_status ON tasks(user_id, status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_notes_user_updated ON notes(user_id, updated_at DESC);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_calendar_events_user_time ON calendar_events(user_id, start_time);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_achievements_user ON user_achievements(user_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_quest_progress_user_status ON user_quest_progress(user_id, status);

-- Partial indexes for better performance
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_tasks_active ON tasks(user_id, updated_at DESC) WHERE status != 'completed';
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_notes_active ON notes(user_id, updated_at DESC) WHERE is_deleted = false;

-- =====================================================
-- COMMENTS FOR DOCUMENTATION
-- =====================================================

COMMENT ON TABLE user_profiles IS 'Core user profile information with privacy settings';
COMMENT ON TABLE notes IS 'User notes with encryption support and folder organization';
COMMENT ON TABLE tasks IS 'Task management with projects, categories, and recurring tasks';
COMMENT ON TABLE calendar_events IS 'Calendar events with recurrence and reminder support';
COMMENT ON TABLE diary_entries IS 'Private daily diary entries with mood tracking';
COMMENT ON TABLE health_records IS 'Health and wellness data tracking';
COMMENT ON TABLE user_progress IS 'Gamification progress tracking per user';
COMMENT ON TABLE achievements IS 'Global achievement definitions';
COMMENT ON TABLE user_achievements IS 'User-specific achievement unlocks';
COMMENT ON TABLE activity_logs IS 'Audit trail for GDPR compliance and security';

-- =====================================================
-- FINAL SECURITY CHECK
-- =====================================================

-- Ensure all user data tables have RLS enabled
DO $$
DECLARE
    table_name TEXT;
    tables_without_rls TEXT[] := ARRAY[]::TEXT[];
BEGIN
    FOR table_name IN
        SELECT tablename
        FROM pg_tables
        WHERE schemaname = 'public'
        AND tablename NOT IN ('achievements', 'quests', 'badges')
    LOOP
        IF NOT (SELECT rowsecurity FROM pg_class WHERE relname = table_name) THEN
            tables_without_rls := array_append(tables_without_rls, table_name);
        END IF;
    END LOOP;

    IF array_length(tables_without_rls, 1) > 0 THEN
        RAISE EXCEPTION 'SECURITY VIOLATION: Tables without RLS: %', array_to_string(tables_without_rls, ', ');
    END IF;

    RAISE NOTICE 'SECURITY CHECK PASSED: All user data tables have RLS enabled';
END $$;
