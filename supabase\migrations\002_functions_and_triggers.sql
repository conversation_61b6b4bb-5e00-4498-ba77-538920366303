-- =====================================================
-- KOI JOYFUL HABITS HUB - ADVANCED FUNCTIONS & TRIGGERS
-- Enterprise Performance & Business Logic
-- =====================================================

-- =====================================================
-- USER PROFILE MANAGEMENT FUNCTIONS
-- =====================================================

-- Function to create user profile on signup
CREATE OR REPLACE FUNCTION create_user_profile()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO user_profiles (id, email, created_at, updated_at)
    VALUES (NEW.id, NEW.email, NOW(), NOW());
    
    -- Create default user progress record
    INSERT INTO user_progress (user_id, created_at, updated_at)
    VALUES (NEW.id, NOW(), NOW());
    
    -- Create default calendar
    INSERT INTO calendars (user_id, name, is_default, created_at, updated_at)
    VALUES (NEW.id, 'My Calendar', true, NOW(), NOW());
    
    -- Log the user creation
    INSERT INTO activity_logs (user_id, action, resource_type, details)
    VALUES (NEW.id, 'user_created', 'user_profile', '{"signup_method": "' || COALESCE(NEW.app_metadata->>'provider', 'email') || '"}');
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for user profile creation
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION create_user_profile();

-- =====================================================
-- GAMIFICATION FUNCTIONS
-- =====================================================

-- Function to calculate XP and level progression
CREATE OR REPLACE FUNCTION update_user_xp(
    p_user_id UUID,
    p_xp_gained INTEGER,
    p_activity_type TEXT DEFAULT 'general'
)
RETURNS JSONB AS $$
DECLARE
    current_progress RECORD;
    new_level INTEGER;
    level_up BOOLEAN := false;
    result JSONB;
BEGIN
    -- Get current progress
    SELECT * INTO current_progress 
    FROM user_progress 
    WHERE user_id = p_user_id;
    
    -- Calculate new XP
    UPDATE user_progress 
    SET 
        total_xp = total_xp + p_xp_gained,
        updated_at = NOW()
    WHERE user_id = p_user_id;
    
    -- Get updated progress
    SELECT * INTO current_progress 
    FROM user_progress 
    WHERE user_id = p_user_id;
    
    -- Calculate new level (every 100 XP = 1 level, with increasing requirements)
    new_level := FLOOR(SQRT(current_progress.total_xp / 100)) + 1;
    
    -- Check if level up occurred
    IF new_level > current_progress.current_level THEN
        level_up := true;
        
        UPDATE user_progress 
        SET 
            current_level = new_level,
            xp_to_next_level = (new_level * 100) - current_progress.total_xp,
            updated_at = NOW()
        WHERE user_id = p_user_id;
        
        -- Log level up activity
        PERFORM log_user_activity(
            'level_up',
            'user_progress',
            p_user_id,
            jsonb_build_object(
                'new_level', new_level,
                'previous_level', current_progress.current_level,
                'total_xp', current_progress.total_xp + p_xp_gained
            )
        );
    ELSE
        UPDATE user_progress 
        SET 
            xp_to_next_level = (current_progress.current_level * 100) - current_progress.total_xp,
            updated_at = NOW()
        WHERE user_id = p_user_id;
    END IF;
    
    -- Log XP gain
    PERFORM log_user_activity(
        'xp_gained',
        'user_progress',
        p_user_id,
        jsonb_build_object(
            'xp_gained', p_xp_gained,
            'activity_type', p_activity_type,
            'total_xp', current_progress.total_xp + p_xp_gained
        )
    );
    
    result := jsonb_build_object(
        'xp_gained', p_xp_gained,
        'total_xp', current_progress.total_xp + p_xp_gained,
        'current_level', new_level,
        'level_up', level_up
    );
    
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update daily streak
CREATE OR REPLACE FUNCTION update_daily_streak(p_user_id UUID)
RETURNS JSONB AS $$
DECLARE
    current_progress RECORD;
    last_activity_date DATE;
    today DATE := CURRENT_DATE;
    streak_broken BOOLEAN := false;
    streak_continued BOOLEAN := false;
BEGIN
    SELECT * INTO current_progress 
    FROM user_progress 
    WHERE user_id = p_user_id;
    
    last_activity_date := current_progress.last_activity_date;
    
    -- Check streak status
    IF last_activity_date IS NULL THEN
        -- First activity ever
        UPDATE user_progress 
        SET 
            daily_streak = 1,
            last_activity_date = today,
            updated_at = NOW()
        WHERE user_id = p_user_id;
        
        streak_continued := true;
        
    ELSIF last_activity_date = today THEN
        -- Already active today, no change needed
        RETURN jsonb_build_object(
            'streak_status', 'already_active_today',
            'daily_streak', current_progress.daily_streak
        );
        
    ELSIF last_activity_date = today - INTERVAL '1 day' THEN
        -- Consecutive day, continue streak
        UPDATE user_progress 
        SET 
            daily_streak = daily_streak + 1,
            longest_streak = GREATEST(longest_streak, daily_streak + 1),
            last_activity_date = today,
            updated_at = NOW()
        WHERE user_id = p_user_id;
        
        streak_continued := true;
        
        -- Award XP for maintaining streak
        PERFORM update_user_xp(p_user_id, 5, 'daily_streak');
        
    ELSE
        -- Streak broken, reset to 1
        UPDATE user_progress 
        SET 
            daily_streak = 1,
            last_activity_date = today,
            updated_at = NOW()
        WHERE user_id = p_user_id;
        
        streak_broken := true;
    END IF;
    
    -- Get updated progress
    SELECT * INTO current_progress 
    FROM user_progress 
    WHERE user_id = p_user_id;
    
    -- Log streak activity
    PERFORM log_user_activity(
        CASE 
            WHEN streak_continued THEN 'streak_continued'
            WHEN streak_broken THEN 'streak_broken'
            ELSE 'streak_started'
        END,
        'user_progress',
        p_user_id,
        jsonb_build_object(
            'daily_streak', current_progress.daily_streak,
            'longest_streak', current_progress.longest_streak
        )
    );
    
    RETURN jsonb_build_object(
        'streak_status', CASE 
            WHEN streak_continued THEN 'continued'
            WHEN streak_broken THEN 'broken'
            ELSE 'started'
        END,
        'daily_streak', current_progress.daily_streak,
        'longest_streak', current_progress.longest_streak
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check and award achievements
CREATE OR REPLACE FUNCTION check_achievements(p_user_id UUID, p_activity_type TEXT)
RETURNS JSONB AS $$
DECLARE
    achievement RECORD;
    user_stats JSONB;
    requirements JSONB;
    achievement_earned BOOLEAN;
    new_achievements UUID[] := ARRAY[]::UUID[];
BEGIN
    -- Build user statistics
    user_stats := jsonb_build_object(
        'tasks_completed', (SELECT COUNT(*) FROM tasks WHERE user_id = p_user_id AND status = 'completed'),
        'notes_created', (SELECT COUNT(*) FROM notes WHERE user_id = p_user_id AND is_deleted = false),
        'diary_entries', (SELECT COUNT(*) FROM diary_entries WHERE user_id = p_user_id),
        'health_records', (SELECT COUNT(*) FROM health_records WHERE user_id = p_user_id),
        'contacts_added', (SELECT COUNT(*) FROM contacts WHERE user_id = p_user_id),
        'content_items', (SELECT COUNT(*) FROM content_items WHERE user_id = p_user_id),
        'daily_streak', (SELECT daily_streak FROM user_progress WHERE user_id = p_user_id),
        'longest_streak', (SELECT longest_streak FROM user_progress WHERE user_id = p_user_id)
    );
    
    -- Check each achievement
    FOR achievement IN 
        SELECT a.* FROM achievements a
        WHERE a.is_active = true
        AND NOT EXISTS (
            SELECT 1 FROM user_achievements ua 
            WHERE ua.user_id = p_user_id AND ua.achievement_id = a.id
        )
    LOOP
        achievement_earned := true;
        requirements := achievement.requirements;
        
        -- Check each requirement
        FOR key IN SELECT jsonb_object_keys(requirements)
        LOOP
            IF (user_stats->>key)::INTEGER < (requirements->>key)::INTEGER THEN
                achievement_earned := false;
                EXIT;
            END IF;
        END LOOP;
        
        -- Award achievement if earned
        IF achievement_earned THEN
            INSERT INTO user_achievements (user_id, achievement_id, earned_at)
            VALUES (p_user_id, achievement.id, NOW());
            
            new_achievements := array_append(new_achievements, achievement.id);
            
            -- Award XP for achievement
            PERFORM update_user_xp(p_user_id, achievement.xp_reward, 'achievement');
            
            -- Log achievement
            PERFORM log_user_activity(
                'achievement_earned',
                'achievement',
                achievement.id,
                jsonb_build_object(
                    'achievement_name', achievement.name,
                    'xp_reward', achievement.xp_reward
                )
            );
        END IF;
    END LOOP;
    
    RETURN jsonb_build_object(
        'new_achievements', new_achievements,
        'total_earned', array_length(new_achievements, 1)
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- TASK MANAGEMENT FUNCTIONS
-- =====================================================

-- Function to handle task completion
CREATE OR REPLACE FUNCTION complete_task(p_task_id UUID)
RETURNS JSONB AS $$
DECLARE
    task_record RECORD;
    xp_reward INTEGER := 10;
    result JSONB;
BEGIN
    -- Get task details
    SELECT * INTO task_record FROM tasks WHERE id = p_task_id;
    
    IF NOT FOUND THEN
        RETURN jsonb_build_object('error', 'Task not found');
    END IF;
    
    IF task_record.status = 'completed' THEN
        RETURN jsonb_build_object('error', 'Task already completed');
    END IF;
    
    -- Update task status
    UPDATE tasks 
    SET 
        status = 'completed',
        completed_at = NOW(),
        completion_percentage = 100,
        updated_at = NOW()
    WHERE id = p_task_id;
    
    -- Calculate XP reward based on priority
    xp_reward := CASE task_record.priority
        WHEN 'urgent' THEN 20
        WHEN 'high' THEN 15
        WHEN 'medium' THEN 10
        WHEN 'low' THEN 5
        ELSE 10
    END;
    
    -- Award XP
    result := update_user_xp(task_record.user_id, xp_reward, 'task_completion');
    
    -- Update daily streak
    PERFORM update_daily_streak(task_record.user_id);
    
    -- Check for achievements
    PERFORM check_achievements(task_record.user_id, 'task_completion');
    
    -- Log task completion
    PERFORM log_user_activity(
        'task_completed',
        'task',
        p_task_id,
        jsonb_build_object(
            'task_title', task_record.title,
            'priority', task_record.priority,
            'xp_earned', xp_reward
        )
    );
    
    RETURN jsonb_build_object(
        'success', true,
        'xp_earned', xp_reward,
        'level_info', result
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- LEADERBOARD FUNCTIONS
-- =====================================================

-- Function to update leaderboards
CREATE OR REPLACE FUNCTION update_leaderboards()
RETURNS VOID AS $$
DECLARE
    current_date DATE := CURRENT_DATE;
    week_start DATE := date_trunc('week', current_date)::DATE;
    month_start DATE := date_trunc('month', current_date)::DATE;
BEGIN
    -- Update daily XP leaderboard
    INSERT INTO leaderboard_entries (user_id, leaderboard_type, score, period, period_start, period_end)
    SELECT 
        up.user_id,
        'daily_xp',
        COALESCE(daily_xp.xp, 0),
        'daily',
        current_date,
        current_date
    FROM user_progress up
    LEFT JOIN (
        SELECT 
            al.user_id,
            SUM((al.details->>'xp_gained')::INTEGER) as xp
        FROM activity_logs al
        WHERE al.action = 'xp_gained'
        AND al.created_at::DATE = current_date
        GROUP BY al.user_id
    ) daily_xp ON up.user_id = daily_xp.user_id
    ON CONFLICT (user_id, leaderboard_type, period, period_start) 
    DO UPDATE SET 
        score = EXCLUDED.score,
        updated_at = NOW();
    
    -- Update weekly streak leaderboard
    INSERT INTO leaderboard_entries (user_id, leaderboard_type, score, period, period_start, period_end)
    SELECT 
        user_id,
        'weekly_streak',
        daily_streak,
        'weekly',
        week_start,
        week_start + INTERVAL '6 days'
    FROM user_progress
    ON CONFLICT (user_id, leaderboard_type, period, period_start) 
    DO UPDATE SET 
        score = EXCLUDED.score,
        updated_at = NOW();
    
    -- Update monthly achievements leaderboard
    INSERT INTO leaderboard_entries (user_id, leaderboard_type, score, period, period_start, period_end)
    SELECT 
        ua.user_id,
        'monthly_achievements',
        COUNT(*),
        'monthly',
        month_start,
        (month_start + INTERVAL '1 month' - INTERVAL '1 day')::DATE
    FROM user_achievements ua
    WHERE ua.earned_at >= month_start
    GROUP BY ua.user_id
    ON CONFLICT (user_id, leaderboard_type, period, period_start) 
    DO UPDATE SET 
        score = EXCLUDED.score,
        updated_at = NOW();
    
    -- Update ranks
    UPDATE leaderboard_entries 
    SET rank = ranked.rank
    FROM (
        SELECT 
            id,
            RANK() OVER (PARTITION BY leaderboard_type, period, period_start ORDER BY score DESC) as rank
        FROM leaderboard_entries
        WHERE period_start >= current_date - INTERVAL '30 days'
    ) ranked
    WHERE leaderboard_entries.id = ranked.id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Schedule leaderboard updates (run daily)
-- This would typically be set up as a cron job or scheduled function
