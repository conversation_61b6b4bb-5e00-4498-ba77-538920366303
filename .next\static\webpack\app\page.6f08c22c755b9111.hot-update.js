"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Header.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Bot_ChevronDown_Globe_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,ChevronDown,Globe,Settings,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_ChevronDown_Globe_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,ChevronDown,Globe,Settings,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_ChevronDown_Globe_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,ChevronDown,Globe,Settings,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_ChevronDown_Globe_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,ChevronDown,Globe,Settings,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_ChevronDown_Globe_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,ChevronDown,Globe,Settings,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _lib_i18n_useTranslation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../lib/i18n/useTranslation */ \"(app-pages-browser)/./src/lib/i18n/useTranslation.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst Header = (param)=>{\n    let { onAIAssistantClick, onAccountClick, onSettingsClick, onLanguageChange, currentLanguage } = param;\n    _s();\n    const { t } = (0,_lib_i18n_useTranslation__WEBPACK_IMPORTED_MODULE_2__.useTranslation)(currentLanguage);\n    const [showLanguageDropdown, setShowLanguageDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAccountDropdown, setShowAccountDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const languages = [\n        {\n            code: 'en',\n            label: 'US English',\n            flag: '🇺🇸'\n        },\n        {\n            code: 'es',\n            label: 'ES Español',\n            flag: '🇪🇸'\n        },\n        {\n            code: 'fr',\n            label: 'FR Français',\n            flag: '🇫🇷'\n        },\n        {\n            code: 'de',\n            label: 'DE Deutsch',\n            flag: '🇩🇪'\n        },\n        {\n            code: 'it',\n            label: 'IT Italiano',\n            flag: '🇮🇹'\n        },\n        {\n            code: 'pt',\n            label: 'PT Português',\n            flag: '🇵🇹'\n        },\n        {\n            code: 'nl',\n            label: 'NL Nederlands',\n            flag: '🇳🇱'\n        },\n        {\n            code: 'zh',\n            label: 'CN 中文',\n            flag: '🇨🇳'\n        },\n        {\n            code: 'ja',\n            label: 'JP 日本語',\n            flag: '🇯🇵'\n        },\n        {\n            code: 'ko',\n            label: 'KR 한국어',\n            flag: '🇰🇷'\n        },\n        {\n            code: 'ru',\n            label: 'RU Русский',\n            flag: '🇷🇺'\n        },\n        {\n            code: 'gr',\n            label: 'GR Ελληνικά',\n            flag: '🇬🇷'\n        },\n        {\n            code: 'he',\n            label: 'IL עברית',\n            flag: '🇮🇱'\n        },\n        {\n            code: 'th',\n            label: 'TH ไทย',\n            flag: '🇹🇭'\n        },\n        {\n            code: 'vi',\n            label: 'VN Tiếng Việt',\n            flag: '🇻🇳'\n        },\n        {\n            code: 'id',\n            label: 'ID Bahasa Indonesia',\n            flag: '🇮🇩'\n        }\n    ];\n    const currentLang = languages.find((lang)=>lang.code === currentLanguage) || languages[0];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-gradient-to-r from-blue-600 via-blue-700 to-blue-800 shadow-lg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-12 h-12 bg-gradient-to-r from-orange-400 to-orange-500 rounded-full flex items-center justify-center shadow-lg animate-bounce\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white font-bold text-xl\",\n                                        children: \"\\uD83D\\uDC20\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-xl font-bold text-white\",\n                                            children: \"Koi App\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-blue-200 text-sm\",\n                                            children: \"Your Ultimate AI Productivity Assistant\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onAIAssistantClick,\n                                    className: \"flex items-center space-x-2 bg-blue-500/80 hover:bg-blue-400/80 px-4 py-2 rounded-lg transition-colors text-white\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_ChevronDown_Globe_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium\",\n                                            children: \"AI Assistant\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowLanguageDropdown(!showLanguageDropdown),\n                                            className: \"flex items-center space-x-2 bg-blue-500/80 hover:bg-blue-400/80 px-4 py-2 rounded-lg transition-colors text-white\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_ChevronDown_Globe_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                    lineNumber: 84,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: currentLang.label\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                    lineNumber: 85,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_ChevronDown_Globe_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                    lineNumber: 86,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 80,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        showLanguageDropdown && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute right-0 mt-2 w-64 bg-blue-600 rounded-xl shadow-lg border border-blue-400/30 z-50 max-h-80 overflow-y-auto\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2\",\n                                                children: languages.map((language)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>{\n                                                            onLanguageChange(language.code);\n                                                            setShowLanguageDropdown(false);\n                                                        },\n                                                        className: \"w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors \".concat(currentLanguage === language.code ? 'bg-blue-400/50 text-white' : 'text-blue-200 hover:bg-blue-500/30 hover:text-white'),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-lg\",\n                                                                children: language.flag\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                                lineNumber: 105,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: language.label\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                                lineNumber: 106,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            currentLanguage === language.code && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"ml-auto text-blue-200\",\n                                                                children: \"✓\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                                lineNumber: 108,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, language.code, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                        lineNumber: 93,\n                                                        columnNumber: 23\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowAccountDropdown(!showAccountDropdown),\n                                            className: \"flex items-center space-x-2 bg-blue-500/80 hover:bg-blue-400/80 px-4 py-2 rounded-lg transition-colors text-white\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_ChevronDown_Globe_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Account\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        showAccountDropdown && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute right-0 mt-2 w-48 bg-blue-600 rounded-xl shadow-lg border border-blue-400/30 z-50\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>{\n                                                        onAccountClick();\n                                                        setShowAccountDropdown(false);\n                                                    },\n                                                    className: \"w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors text-blue-200 hover:bg-blue-500/30 hover:text-white\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium\",\n                                                        children: \"Sign Out\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                        lineNumber: 137,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                    lineNumber: 130,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onSettingsClick,\n                                    className: \"flex items-center space-x-2 bg-blue-500/80 hover:bg-blue-400/80 px-4 py-2 rounded-lg transition-colors text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_ChevronDown_Globe_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, undefined),\n            (showLanguageDropdown || showAccountDropdown) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-40\",\n                onClick: ()=>{\n                    setShowLanguageDropdown(false);\n                    setShowAccountDropdown(false);\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                lineNumber: 157,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Header, \"Bo+3LWYfkA7GnNUfmlhJ3KwTatg=\", false, function() {\n    return [\n        _lib_i18n_useTranslation__WEBPACK_IMPORTED_MODULE_2__.useTranslation\n    ];\n});\n_c = Header;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Header);\nvar _c;\n$RefreshReg$(_c, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/Header.tsx\n"));

/***/ })

});