'use client';

import { useState } from 'react';
import { Bot, Globe, User, Settings, ChevronDown } from 'lucide-react';
import { useTranslation } from '../../lib/i18n/useTranslation';

interface HeaderProps {
  onAIAssistantClick: () => void;
  onAccountClick: () => void;
  onSettingsClick: () => void;
  onLanguageChange: (language: string) => void;
  currentLanguage: string;
}

const Header = ({
  onAIAssistantClick,
  onAccountClick,
  onSettingsClick,
  onLanguageChange,
  currentLanguage
}: HeaderProps) => {
  const { t } = useTranslation(currentLanguage as any);
  const [showLanguageDropdown, setShowLanguageDropdown] = useState(false);
  const [showAccountDropdown, setShowAccountDropdown] = useState(false);

  const languages = [
    { code: 'en', label: 'US English', flag: '🇺🇸' },
    { code: 'es', label: 'ES Español', flag: '🇪🇸' },
    { code: 'fr', label: 'FR Français', flag: '🇫🇷' },
    { code: 'de', label: 'DE Deutsch', flag: '🇩🇪' },
    { code: 'it', label: 'IT Italiano', flag: '🇮🇹' },
    { code: 'pt', label: 'PT Português', flag: '🇵🇹' },
    { code: 'nl', label: 'NL Nederlands', flag: '🇳🇱' },
    { code: 'zh', label: 'CN 中文', flag: '🇨🇳' },
    { code: 'ja', label: 'JP 日本語', flag: '🇯🇵' },
    { code: 'ko', label: 'KR 한국어', flag: '🇰🇷' },
    { code: 'ru', label: 'RU Русский', flag: '🇷🇺' },
    { code: 'gr', label: 'GR Ελληνικά', flag: '🇬🇷' },
    { code: 'he', label: 'IL עברית', flag: '🇮🇱' },
    { code: 'th', label: 'TH ไทย', flag: '🇹🇭' },
    { code: 'vi', label: 'VN Tiếng Việt', flag: '🇻🇳' },
    { code: 'id', label: 'ID Bahasa Indonesia', flag: '🇮🇩' },
  ];

  const currentLang = languages.find(lang => lang.code === currentLanguage) || languages[0];

  return (
    <header className="bg-gradient-to-r from-blue-600 via-blue-700 to-blue-800 shadow-lg border-b border-blue-500/30">
      <div className="max-w-7xl mx-auto px-6 lg:px-8">
        <div className="flex justify-between items-center h-20">
          {/* Logo and Title */}
          <div className="flex items-center space-x-4">
            {/* Animated Koi Fish Logo - Slow bobbing animation */}
            <div className="relative">
              <div className="w-14 h-14 bg-gradient-to-br from-cyan-400 via-blue-500 to-purple-600 rounded-full flex items-center justify-center shadow-xl animate-pulse">
                <div className="w-12 h-12 bg-gradient-to-br from-cyan-300 to-blue-400 rounded-full flex items-center justify-center animate-bounce" style={{ animationDuration: '3s' }}>
                  <span className="text-white font-bold text-2xl">🐠</span>
                </div>
              </div>
              {/* Glow effect */}
              <div className="absolute inset-0 w-14 h-14 bg-cyan-400/20 rounded-full blur-lg animate-pulse"></div>
            </div>
            <div>
              <h1 className="text-2xl font-bold text-white tracking-tight">
                {t('appName')}
              </h1>
              <p className="text-blue-200 text-sm font-medium">
                {t('appSubtitle')}
              </p>
            </div>
          </div>

          {/* Right Navigation */}
          <div className="flex items-center space-x-2">
            {/* AI Assistant Button */}
            <button
              onClick={onAIAssistantClick}
              className="flex items-center space-x-2 bg-blue-500/70 hover:bg-blue-400/80 px-4 py-2.5 rounded-xl transition-all duration-200 text-white shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 border border-blue-400/30"
            >
              <Bot className="w-5 h-5" />
              <span className="text-sm font-semibold">{t('aiAssistant')}</span>
            </button>

            {/* Language Selector */}
            <div className="relative">
              <button
                onClick={() => setShowLanguageDropdown(!showLanguageDropdown)}
                className="flex items-center space-x-2 bg-blue-500/70 hover:bg-blue-400/80 px-4 py-2.5 rounded-xl transition-all duration-200 text-white shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 border border-blue-400/30"
              >
                <Globe className="w-5 h-5" />
                <span className="text-sm font-semibold">{currentLang.label}</span>
                <ChevronDown className="w-4 h-4" />
              </button>

              {showLanguageDropdown && (
                <div className="absolute right-0 mt-3 w-72 bg-gradient-to-br from-blue-600 to-blue-700 rounded-2xl shadow-2xl border border-blue-400/30 z-50 max-h-80 overflow-y-auto backdrop-blur-sm">
                  <div className="p-3">
                    <div className="text-blue-200 text-xs font-semibold uppercase tracking-wider mb-2 px-3">Select Language</div>
                    {languages.map((language) => (
                      <button
                        key={language.code}
                        onClick={() => {
                          onLanguageChange(language.code);
                          setShowLanguageDropdown(false);
                        }}
                        className={`w-full flex items-center space-x-3 px-4 py-3 rounded-xl text-left transition-all duration-200 ${
                          currentLanguage === language.code
                            ? 'bg-blue-400/60 text-white shadow-lg'
                            : 'text-blue-200 hover:bg-blue-500/40 hover:text-white'
                        }`}
                      >
                        <span className="text-lg">{language.flag}</span>
                        <span className="text-sm font-semibold">{language.label}</span>
                        {currentLanguage === language.code && (
                          <span className="ml-auto text-cyan-300 font-bold">✓</span>
                        )}
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Account Button */}
            <div className="relative">
              <button
                onClick={() => setShowAccountDropdown(!showAccountDropdown)}
                className="flex items-center space-x-2 bg-blue-500/70 hover:bg-blue-400/80 px-4 py-2.5 rounded-xl transition-all duration-200 text-white shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 border border-blue-400/30"
              >
                <User className="w-5 h-5" />
                <span className="text-sm font-semibold">{t('account')}</span>
              </button>

              {showAccountDropdown && (
                <div className="absolute right-0 mt-3 w-52 bg-gradient-to-br from-blue-600 to-blue-700 rounded-2xl shadow-2xl border border-blue-400/30 z-50 backdrop-blur-sm">
                  <div className="p-3">
                    <div className="text-blue-200 text-xs font-semibold uppercase tracking-wider mb-2 px-3">Account</div>
                    <button
                      onClick={() => {
                        onAccountClick();
                        setShowAccountDropdown(false);
                      }}
                      className="w-full flex items-center space-x-3 px-4 py-3 rounded-xl text-left transition-all duration-200 text-blue-200 hover:bg-blue-500/40 hover:text-white"
                    >
                      <span className="text-sm font-semibold">{t('signOut')}</span>
                    </button>
                  </div>
                </div>
              )}
            </div>

            {/* Settings Button - Gear icon only */}
            <button
              onClick={onSettingsClick}
              className="flex items-center justify-center bg-blue-500/70 hover:bg-blue-400/80 p-3 rounded-xl transition-all duration-200 text-white shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 border border-blue-400/30"
            >
              <Settings className="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>

      {/* Click outside to close dropdowns */}
      {(showLanguageDropdown || showAccountDropdown) && (
        <div 
          className="fixed inset-0 z-40" 
          onClick={() => {
            setShowLanguageDropdown(false);
            setShowAccountDropdown(false);
          }}
        />
      )}
    </header>
  );
};

export default Header;
