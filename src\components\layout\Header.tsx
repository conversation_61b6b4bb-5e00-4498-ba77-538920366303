'use client';

import { useState } from 'react';
import { Bot, Globe, User, Settings, ChevronDown } from 'lucide-react';
import { useTranslation } from '../../lib/i18n/useTranslation';

interface HeaderProps {
  onAIAssistantClick: () => void;
  onAccountClick: () => void;
  onSettingsClick: () => void;
  onLanguageChange: (language: string) => void;
  currentLanguage: string;
}

const Header = ({
  onAIAssistantClick,
  onAccountClick,
  onSettingsClick,
  onLanguageChange,
  currentLanguage
}: HeaderProps) => {
  const { t } = useTranslation(currentLanguage as any);
  const [showLanguageDropdown, setShowLanguageDropdown] = useState(false);
  const [showAccountDropdown, setShowAccountDropdown] = useState(false);

  const languages = [
    { code: 'en', label: 'US English', flag: '🇺🇸' },
    { code: 'es', label: 'ES Español', flag: '🇪🇸' },
    { code: 'fr', label: 'FR Français', flag: '🇫🇷' },
    { code: 'de', label: 'DE Deutsch', flag: '🇩🇪' },
    { code: 'it', label: 'IT Italiano', flag: '🇮🇹' },
    { code: 'pt', label: 'PT Português', flag: '🇵🇹' },
    { code: 'nl', label: 'NL Nederlands', flag: '🇳🇱' },
    { code: 'zh', label: 'CN 中文', flag: '🇨🇳' },
    { code: 'ja', label: 'JP 日本語', flag: '🇯🇵' },
    { code: 'ko', label: 'KR 한국어', flag: '🇰🇷' },
    { code: 'ru', label: 'RU Русский', flag: '🇷🇺' },
    { code: 'gr', label: 'GR Ελληνικά', flag: '🇬🇷' },
    { code: 'he', label: 'IL עברית', flag: '🇮🇱' },
    { code: 'th', label: 'TH ไทย', flag: '🇹🇭' },
    { code: 'vi', label: 'VN Tiếng Việt', flag: '🇻🇳' },
    { code: 'id', label: 'ID Bahasa Indonesia', flag: '🇮🇩' },
  ];

  const currentLang = languages.find(lang => lang.code === currentLanguage) || languages[0];

  return (
    <header className="bg-gradient-to-r from-blue-600 via-blue-700 to-blue-800 shadow-lg">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo and Title */}
          <div className="flex items-center space-x-3">
            {/* Animated Koi Fish Logo */}
            <div className="w-12 h-12 bg-gradient-to-r from-orange-400 to-orange-500 rounded-full flex items-center justify-center shadow-lg animate-bounce">
              <span className="text-white font-bold text-xl">🐠</span>
            </div>
            <div>
              <h1 className="text-xl font-bold text-white">
                {t('appName')}
              </h1>
              <p className="text-blue-200 text-sm">
                {t('appSubtitle')}
              </p>
            </div>
          </div>

          {/* Right Navigation */}
          <div className="flex items-center space-x-3">
            {/* AI Assistant Button */}
            <button
              onClick={onAIAssistantClick}
              className="flex items-center space-x-2 bg-blue-500/80 hover:bg-blue-400/80 px-4 py-2 rounded-lg transition-colors text-white"
            >
              <Bot className="w-4 h-4" />
              <span className="text-sm font-medium">{t('aiAssistant')}</span>
            </button>

            {/* Language Selector */}
            <div className="relative">
              <button 
                onClick={() => setShowLanguageDropdown(!showLanguageDropdown)}
                className="flex items-center space-x-2 bg-blue-500/80 hover:bg-blue-400/80 px-4 py-2 rounded-lg transition-colors text-white"
              >
                <Globe className="w-4 h-4" />
                <span className="text-sm font-medium">{currentLang.label}</span>
                <ChevronDown className="w-4 h-4" />
              </button>

              {showLanguageDropdown && (
                <div className="absolute right-0 mt-2 w-64 bg-blue-600 rounded-xl shadow-lg border border-blue-400/30 z-50 max-h-80 overflow-y-auto">
                  <div className="p-2">
                    {languages.map((language) => (
                      <button
                        key={language.code}
                        onClick={() => {
                          onLanguageChange(language.code);
                          setShowLanguageDropdown(false);
                        }}
                        className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${
                          currentLanguage === language.code 
                            ? 'bg-blue-400/50 text-white' 
                            : 'text-blue-200 hover:bg-blue-500/30 hover:text-white'
                        }`}
                      >
                        <span className="text-lg">{language.flag}</span>
                        <span className="text-sm font-medium">{language.label}</span>
                        {currentLanguage === language.code && (
                          <span className="ml-auto text-blue-200">✓</span>
                        )}
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Account Button */}
            <div className="relative">
              <button
                onClick={() => setShowAccountDropdown(!showAccountDropdown)}
                className="flex items-center space-x-2 bg-blue-500/80 hover:bg-blue-400/80 px-4 py-2 rounded-lg transition-colors text-white"
              >
                <User className="w-4 h-4" />
                <span className="text-sm font-medium">{t('account')}</span>
              </button>

              {showAccountDropdown && (
                <div className="absolute right-0 mt-2 w-48 bg-blue-600 rounded-xl shadow-lg border border-blue-400/30 z-50">
                  <div className="p-2">
                    <button
                      onClick={() => {
                        onAccountClick();
                        setShowAccountDropdown(false);
                      }}
                      className="w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors text-blue-200 hover:bg-blue-500/30 hover:text-white"
                    >
                      <span className="text-sm font-medium">{t('signOut')}</span>
                    </button>
                  </div>
                </div>
              )}
            </div>

            {/* Settings Button */}
            <button 
              onClick={onSettingsClick}
              className="flex items-center space-x-2 bg-blue-500/80 hover:bg-blue-400/80 px-4 py-2 rounded-lg transition-colors text-white"
            >
              <Settings className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Click outside to close dropdowns */}
      {(showLanguageDropdown || showAccountDropdown) && (
        <div 
          className="fixed inset-0 z-40" 
          onClick={() => {
            setShowLanguageDropdown(false);
            setShowAccountDropdown(false);
          }}
        />
      )}
    </header>
  );
};

export default Header;
