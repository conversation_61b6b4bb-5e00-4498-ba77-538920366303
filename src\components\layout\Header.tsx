'use client';

import { useState } from 'react';
import { Bot, Globe, User, Settings, ChevronDown } from 'lucide-react';
import { useTranslation } from '../../lib/i18n/useTranslation';

interface HeaderProps {
  onAIAssistantClick: () => void;
  onAccountClick: () => void;
  onSettingsClick: () => void;
  onLanguageChange: (language: string) => void;
  currentLanguage: string;
}

const Header = ({
  onAIAssistantClick,
  onAccountClick,
  onSettingsClick,
  onLanguageChange,
  currentLanguage
}: HeaderProps) => {
  const { t } = useTranslation(currentLanguage as any);
  const [showLanguageDropdown, setShowLanguageDropdown] = useState(false);
  const [showAccountDropdown, setShowAccountDropdown] = useState(false);

  const languages = [
    { code: 'en', label: 'US English', flag: '🇺🇸' },
    { code: 'es', label: 'ES Español', flag: '🇪🇸' },
    { code: 'fr', label: 'FR Français', flag: '🇫🇷' },
    { code: 'de', label: 'DE Deutsch', flag: '🇩🇪' },
    { code: 'it', label: 'IT Italiano', flag: '🇮🇹' },
    { code: 'pt', label: 'PT Português', flag: '🇵🇹' },
    { code: 'nl', label: 'NL Nederlands', flag: '🇳🇱' },
    { code: 'zh', label: 'CN 中文', flag: '🇨🇳' },
    { code: 'ja', label: 'JP 日本語', flag: '🇯🇵' },
    { code: 'ko', label: 'KR 한국어', flag: '🇰🇷' },
    { code: 'ru', label: 'RU Русский', flag: '🇷🇺' },
    { code: 'gr', label: 'GR Ελληνικά', flag: '🇬🇷' },
    { code: 'he', label: 'IL עברית', flag: '🇮🇱' },
    { code: 'th', label: 'TH ไทย', flag: '🇹🇭' },
    { code: 'vi', label: 'VN Tiếng Việt', flag: '🇻🇳' },
    { code: 'id', label: 'ID Bahasa Indonesia', flag: '🇮🇩' },
  ];

  const currentLang = languages.find(lang => lang.code === currentLanguage) || languages[0];

  return (
    <header className="bg-gradient-to-r from-blue-600 via-blue-700 to-blue-800 shadow-lg border-b border-blue-500/30">
      <div className="max-w-7xl mx-auto px-6 lg:px-8">
        <div className="flex justify-between items-center h-20">
          {/* Logo and Title */}
          <div className="flex items-center space-x-4">
            {/* Animated Koi Fish Logo - Slow bobbing animation */}
            <div className="relative">
              <div className="w-14 h-14 bg-gradient-to-br from-cyan-300 via-blue-400 to-purple-500 rounded-full flex items-center justify-center shadow-xl animate-bounce border-2 border-white/20" style={{ animationDuration: '3s' }}>
                {/* Koi Fish SVG */}
                <svg width="32" height="32" viewBox="0 0 100 100" className="drop-shadow-lg">
                  {/* Koi body - cyan to purple gradient */}
                  <ellipse cx="40" cy="50" rx="25" ry="20" fill="url(#koiGradientHeader)" />

                  {/* Tail fin */}
                  <path d="M15 50 Q5 40 10 30 Q15 35 20 40 Q15 45 10 50 Q15 55 20 60 Q15 65 10 70 Q5 60 15 50" fill="url(#finGradientHeader)" />

                  {/* Top fin */}
                  <path d="M35 30 Q30 20 40 25 Q45 30 35 30" fill="url(#finGradientHeader)" />

                  {/* Side fins */}
                  <ellipse cx="30" cy="60" rx="8" ry="4" fill="url(#finGradientHeader)" transform="rotate(30 30 60)" />
                  <ellipse cx="50" cy="60" rx="8" ry="4" fill="url(#finGradientHeader)" transform="rotate(-30 50 60)" />

                  {/* Eyes */}
                  <circle cx="45" cy="45" r="4" fill="white" />
                  <circle cx="45" cy="45" r="2.5" fill="black" />
                  <circle cx="46" cy="44" r="1" fill="white" />

                  {/* Spots */}
                  <circle cx="35" cy="40" r="2" fill="#90EE90" opacity="0.8" />
                  <circle cx="40" cy="35" r="1.5" fill="#90EE90" opacity="0.8" />
                  <circle cx="30" cy="50" r="1.5" fill="#90EE90" opacity="0.8" />

                  {/* Mouth */}
                  <path d="M55 50 Q60 52 55 54" stroke="black" strokeWidth="1" fill="none" />

                  {/* Gradients */}
                  <defs>
                    <linearGradient id="koiGradientHeader" x1="0%" y1="0%" x2="100%" y2="100%">
                      <stop offset="0%" stopColor="#00FFFF" />
                      <stop offset="50%" stopColor="#0080FF" />
                      <stop offset="100%" stopColor="#8000FF" />
                    </linearGradient>
                    <linearGradient id="finGradientHeader" x1="0%" y1="0%" x2="100%" y2="100%">
                      <stop offset="0%" stopColor="#FF69B4" />
                      <stop offset="50%" stopColor="#DA70D6" />
                      <stop offset="100%" stopColor="#9370DB" />
                    </linearGradient>
                  </defs>
                </svg>
              </div>
            </div>
            <div>
              <h1 className="text-2xl font-bold text-white tracking-tight">
                {t('appName')}
              </h1>
              <p className="text-blue-200 text-sm font-medium">
                {t('appSubtitle')}
              </p>
            </div>
          </div>

          {/* Right Navigation */}
          <div className="flex items-center space-x-2">
            {/* AI Assistant Button */}
            <button
              onClick={onAIAssistantClick}
              className="flex items-center space-x-2 bg-blue-500/70 hover:bg-blue-400/80 px-4 py-2.5 rounded-xl transition-all duration-200 text-white shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 border border-blue-400/30"
            >
              <Bot className="w-5 h-5" />
              <span className="text-sm font-semibold">{t('aiAssistant')}</span>
            </button>

            {/* Language Selector */}
            <div className="relative">
              <button
                onClick={() => setShowLanguageDropdown(!showLanguageDropdown)}
                className="flex items-center space-x-2 bg-blue-500/70 hover:bg-blue-400/80 px-4 py-2.5 rounded-xl transition-all duration-200 text-white shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 border border-blue-400/30"
              >
                <Globe className="w-5 h-5" />
                <span className="text-sm font-semibold">{currentLang.label}</span>
                <ChevronDown className="w-4 h-4" />
              </button>

              {showLanguageDropdown && (
                <div className="absolute right-0 mt-3 w-72 bg-gradient-to-br from-blue-600 to-blue-700 rounded-2xl shadow-2xl border border-blue-400/30 z-50 max-h-80 overflow-y-auto backdrop-blur-sm">
                  <div className="p-3">
                    <div className="text-blue-200 text-xs font-semibold uppercase tracking-wider mb-2 px-3">Select Language</div>
                    {languages.map((language) => (
                      <button
                        key={language.code}
                        onClick={() => {
                          onLanguageChange(language.code);
                          setShowLanguageDropdown(false);
                        }}
                        className={`w-full flex items-center space-x-3 px-4 py-3 rounded-xl text-left transition-all duration-200 ${
                          currentLanguage === language.code
                            ? 'bg-blue-400/60 text-white shadow-lg'
                            : 'text-blue-200 hover:bg-blue-500/40 hover:text-white'
                        }`}
                      >
                        <span className="text-lg">{language.flag}</span>
                        <span className="text-sm font-semibold">{language.label}</span>
                        {currentLanguage === language.code && (
                          <span className="ml-auto text-cyan-300 font-bold">✓</span>
                        )}
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Account Button */}
            <div className="relative">
              <button
                onClick={() => setShowAccountDropdown(!showAccountDropdown)}
                className="flex items-center space-x-2 bg-blue-500/70 hover:bg-blue-400/80 px-4 py-2.5 rounded-xl transition-all duration-200 text-white shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 border border-blue-400/30"
              >
                <User className="w-5 h-5" />
                <span className="text-sm font-semibold">{t('account')}</span>
              </button>

              {showAccountDropdown && (
                <div className="absolute right-0 mt-3 w-52 bg-gradient-to-br from-blue-600 to-blue-700 rounded-2xl shadow-2xl border border-blue-400/30 z-50 backdrop-blur-sm">
                  <div className="p-3">
                    <div className="text-blue-200 text-xs font-semibold uppercase tracking-wider mb-2 px-3">Account</div>
                    <button
                      onClick={() => {
                        onAccountClick();
                        setShowAccountDropdown(false);
                      }}
                      className="w-full flex items-center space-x-3 px-4 py-3 rounded-xl text-left transition-all duration-200 text-blue-200 hover:bg-blue-500/40 hover:text-white"
                    >
                      <span className="text-sm font-semibold">{t('signOut')}</span>
                    </button>
                  </div>
                </div>
              )}
            </div>

            {/* Settings Button - Gear icon only */}
            <button
              onClick={onSettingsClick}
              className="flex items-center justify-center bg-blue-500/70 hover:bg-blue-400/80 p-3 rounded-xl transition-all duration-200 text-white shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 border border-blue-400/30"
            >
              <Settings className="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>

      {/* Click outside to close dropdowns */}
      {(showLanguageDropdown || showAccountDropdown) && (
        <div 
          className="fixed inset-0 z-40" 
          onClick={() => {
            setShowLanguageDropdown(false);
            setShowAccountDropdown(false);
          }}
        />
      )}
    </header>
  );
};

export default Header;
