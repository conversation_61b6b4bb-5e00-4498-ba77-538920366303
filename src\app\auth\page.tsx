'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '../../contexts/AuthContext';
import Auth from '../../components/pages/Auth';

export default function AuthPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user, loading } = useAuth();
  const [currentLanguage, setCurrentLanguage] = useState('en');

  // Load saved language preference
  useEffect(() => {
    const savedLanguage = localStorage.getItem('koi-app-language');
    if (savedLanguage) {
      setCurrentLanguage(savedLanguage);
    }
  }, []);

  // Save language preference
  const handleLanguageChange = (language: string) => {
    setCurrentLanguage(language);
    localStorage.setItem('koi-app-language', language);
  };

  // Redirect to dashboard if already authenticated
  useEffect(() => {
    if (!loading && user) {
      router.push('/');
    }
  }, [user, loading, router]);

  // Show loading while checking auth state
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800 flex items-center justify-center">
        <div className="text-center">
          {/* Animated Koi Logo */}
          <div className="w-16 h-16 bg-gradient-to-br from-cyan-400 via-blue-500 to-purple-600 rounded-full flex items-center justify-center shadow-2xl animate-pulse mx-auto mb-6">
            <div className="w-14 h-14 bg-gradient-to-br from-cyan-300 to-blue-400 rounded-full flex items-center justify-center animate-bounce" style={{ animationDuration: '3s' }}>
              <span className="text-white font-bold text-2xl">🐠</span>
            </div>
          </div>
          
          <h2 className="text-2xl font-bold text-white mb-4">Loading...</h2>
          <div className="w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin mx-auto"></div>
        </div>
      </div>
    );
  }

  // Don't render auth page if user is authenticated
  if (user) {
    return null;
  }

  return (
    <Auth 
      language={currentLanguage}
      onLanguageChange={handleLanguageChange}
    />
  );
}
