'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '../../contexts/AuthContext';
import Auth from '../../components/pages/Auth';

export default function AuthPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user, loading } = useAuth();
  const [currentLanguage, setCurrentLanguage] = useState('en');

  // Load saved language preference
  useEffect(() => {
    const savedLanguage = localStorage.getItem('koi-app-language');
    if (savedLanguage) {
      setCurrentLanguage(savedLanguage);
    }
  }, []);

  // Save language preference
  const handleLanguageChange = (language: string) => {
    setCurrentLanguage(language);
    localStorage.setItem('koi-app-language', language);
  };

  // Redirect to dashboard if already authenticated
  useEffect(() => {
    if (!loading && user) {
      router.push('/');
    }
  }, [user, loading, router]);

  // Show loading while checking auth state
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800 flex items-center justify-center">
        <div className="text-center">
          {/* Animated Koi Logo */}
          <div className="w-16 h-16 bg-gradient-to-br from-cyan-300 via-blue-400 to-purple-500 rounded-full flex items-center justify-center shadow-2xl mx-auto mb-6 animate-bounce border-2 border-white/20" style={{ animationDuration: '3s' }}>
            {/* Koi Fish SVG */}
            <svg width="40" height="40" viewBox="0 0 100 100" className="drop-shadow-lg">
              {/* Koi body - cyan to purple gradient */}
              <ellipse cx="40" cy="50" rx="25" ry="20" fill="url(#koiGradientAuthPage)" />

              {/* Tail fin */}
              <path d="M15 50 Q5 40 10 30 Q15 35 20 40 Q15 45 10 50 Q15 55 20 60 Q15 65 10 70 Q5 60 15 50" fill="url(#finGradientAuthPage)" />

              {/* Top fin */}
              <path d="M35 30 Q30 20 40 25 Q45 30 35 30" fill="url(#finGradientAuthPage)" />

              {/* Side fins */}
              <ellipse cx="30" cy="60" rx="8" ry="4" fill="url(#finGradientAuthPage)" transform="rotate(30 30 60)" />
              <ellipse cx="50" cy="60" rx="8" ry="4" fill="url(#finGradientAuthPage)" transform="rotate(-30 50 60)" />

              {/* Eyes */}
              <circle cx="45" cy="45" r="4" fill="white" />
              <circle cx="45" cy="45" r="2.5" fill="black" />
              <circle cx="46" cy="44" r="1" fill="white" />

              {/* Spots */}
              <circle cx="35" cy="40" r="2" fill="#90EE90" opacity="0.8" />
              <circle cx="40" cy="35" r="1.5" fill="#90EE90" opacity="0.8" />
              <circle cx="30" cy="50" r="1.5" fill="#90EE90" opacity="0.8" />

              {/* Mouth */}
              <path d="M55 50 Q60 52 55 54" stroke="black" strokeWidth="1" fill="none" />

              {/* Gradients */}
              <defs>
                <linearGradient id="koiGradientAuthPage" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" stopColor="#00FFFF" />
                  <stop offset="50%" stopColor="#0080FF" />
                  <stop offset="100%" stopColor="#8000FF" />
                </linearGradient>
                <linearGradient id="finGradientAuthPage" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" stopColor="#FF69B4" />
                  <stop offset="50%" stopColor="#DA70D6" />
                  <stop offset="100%" stopColor="#9370DB" />
                </linearGradient>
              </defs>
            </svg>
          </div>
          
          <h2 className="text-2xl font-bold text-white mb-4">Loading...</h2>
          <div className="w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin mx-auto"></div>
        </div>
      </div>
    );
  }

  // Don't render auth page if user is authenticated
  if (user) {
    return null;
  }

  return (
    <Auth 
      language={currentLanguage}
      onLanguageChange={handleLanguageChange}
    />
  );
}
