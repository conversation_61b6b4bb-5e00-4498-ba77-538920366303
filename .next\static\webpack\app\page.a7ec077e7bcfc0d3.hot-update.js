"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/layout/AppLayout.tsx":
/*!*********************************************!*\
  !*** ./src/components/layout/AppLayout.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _pages_Dashboard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../pages/Dashboard */ \"(app-pages-browser)/./src/components/pages/Dashboard.tsx\");\n/* harmony import */ var _pages_Notes__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../pages/Notes */ \"(app-pages-browser)/./src/components/pages/Notes.tsx\");\n/* harmony import */ var _pages_Settings__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../pages/Settings */ \"(app-pages-browser)/./src/components/pages/Settings.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst AppLayout = (param)=>{\n    let { children } = param;\n    _s();\n    const [activeSection, setActiveSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('dashboard');\n    const [showAIChat, setShowAIChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentLanguage, setCurrentLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('en');\n    // Load saved language preference\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppLayout.useEffect\": ()=>{\n            const savedLanguage = localStorage.getItem('koi-app-language');\n            if (savedLanguage) {\n                setCurrentLanguage(savedLanguage);\n            }\n        }\n    }[\"AppLayout.useEffect\"], []);\n    // Save language preference\n    const handleLanguageChange = (language)=>{\n        setCurrentLanguage(language);\n        localStorage.setItem('koi-app-language', language);\n    };\n    const handleAIAssistantClick = ()=>{\n        setShowAIChat(true);\n    };\n    const handleAccountClick = ()=>{\n        // Handle sign out\n        console.log('Sign out clicked');\n    // You can add actual sign out logic here\n    };\n    const handleSettingsClick = ()=>{\n        setActiveSection('settings');\n    };\n    const renderContent = ()=>{\n        switch(activeSection){\n            case 'dashboard':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_pages_Dashboard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 16\n                }, undefined);\n            case 'notes':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_pages_Notes__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 16\n                }, undefined);\n            case 'tasks':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-white\",\n                        children: \"Tasks - Coming Soon!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 51\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 16\n                }, undefined);\n            case 'content':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-white\",\n                        children: \"Content Creator - Coming Soon!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 51\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 16\n                }, undefined);\n            case 'calendar':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-white\",\n                        children: \"Calendar - Coming Soon!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 51\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-white\",\n                        children: [\n                            activeSection,\n                            \" - Coming Soon!\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 51\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-gradient-to-r from-blue-600 via-blue-700 to-blue-800 shadow-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-gradient-to-r from-orange-400 to-orange-500 rounded-full flex items-center justify-center shadow-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white font-bold text-lg\",\n                                            children: \"\\uD83D\\uDC20\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-bold text-white\",\n                                                children: \"Koi App\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n                                                lineNumber: 76,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-200 text-sm\",\n                                                children: \"Your Joyful Habits Hub\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n                                                lineNumber: 79,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"flex items-center space-x-2 bg-blue-500/30 hover:bg-blue-500/50 px-3 py-2 rounded-lg transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Bot, {\n                                                className: \"w-4 h-4 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n                                                lineNumber: 88,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-sm font-medium\",\n                                                children: \"AI Assistant\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"flex items-center space-x-2 bg-blue-500/30 hover:bg-blue-500/50 px-3 py-2 rounded-lg transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Globe, {\n                                                className: \"w-4 h-4 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n                                                lineNumber: 93,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-sm font-medium\",\n                                                children: \"EN\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n                                                lineNumber: 94,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"flex items-center space-x-2 bg-blue-500/30 hover:bg-blue-500/50 px-3 py-2 rounded-lg transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(User, {\n                                                className: \"w-4 h-4 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-sm font-medium\",\n                                                children: \"Account\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n                                                lineNumber: 99,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"flex items-center space-x-2 bg-blue-500/30 hover:bg-blue-500/50 px-3 py-2 rounded-lg transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_pages_Settings__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-4 h-4 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n                                                lineNumber: 103,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white text-sm font-medium\",\n                                                children: \"Settings\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: children || renderContent()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n                lineNumber: 112,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n        lineNumber: 65,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AppLayout, \"uXcGXh1QDrNmucpMRGmSdNd8oq4=\");\n_c = AppLayout;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AppLayout);\nvar _c;\n$RefreshReg$(_c, \"AppLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/AppLayout.tsx\n"));

/***/ })

});