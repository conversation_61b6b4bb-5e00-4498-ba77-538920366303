'use client';

import { useState, ReactNode, useEffect } from 'react';
import Dashboard from '../pages/Dashboard';
import Notes from '../pages/Notes';
import Settings from '../pages/Settings';
import Header from './Header';
import AIAssistantChat from '../ui/AIAssistantChat';

interface AppLayoutProps {
  children?: ReactNode;
}

const AppLayout = ({ children }: AppLayoutProps) => {
  const [activeSection, setActiveSection] = useState('dashboard');
  const [showAIChat, setShowAIChat] = useState(false);
  const [currentLanguage, setCurrentLanguage] = useState('en');

  // Load saved language preference
  useEffect(() => {
    const savedLanguage = localStorage.getItem('koi-app-language');
    if (savedLanguage) {
      setCurrentLanguage(savedLanguage);
    }
  }, []);

  // Save language preference
  const handleLanguageChange = (language: string) => {
    setCurrentLanguage(language);
    localStorage.setItem('koi-app-language', language);
  };

  const handleAIAssistantClick = () => {
    setShowAIChat(true);
  };

  const handleAccountClick = () => {
    // Handle sign out
    console.log('Sign out clicked');
    // You can add actual sign out logic here
  };

  const handleSettingsClick = () => {
    setActiveSection('settings');
  };

  const renderContent = () => {
    switch (activeSection) {
      case 'dashboard':
        return <Dashboard language={currentLanguage} />;
      case 'notes':
        return <Notes />;
      case 'settings':
        return (
          <Settings
            onBack={() => setActiveSection('dashboard')}
            currentLanguage={currentLanguage}
            onLanguageChange={handleLanguageChange}
          />
        );
      case 'tasks':
        return <div className="text-center py-12"><h2 className="text-2xl font-bold text-white">Tasks - Coming Soon!</h2></div>;
      case 'content':
        return <div className="text-center py-12"><h2 className="text-2xl font-bold text-white">Content Creator - Coming Soon!</h2></div>;
      case 'calendar':
        return <div className="text-center py-12"><h2 className="text-2xl font-bold text-white">Calendar - Coming Soon!</h2></div>;
      default:
        return <div className="text-center py-12"><h2 className="text-2xl font-bold text-white">{activeSection} - Coming Soon!</h2></div>;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800">
      {/* New Header */}
      <Header
        onAIAssistantClick={handleAIAssistantClick}
        onAccountClick={handleAccountClick}
        onSettingsClick={handleSettingsClick}
        onLanguageChange={handleLanguageChange}
        currentLanguage={currentLanguage}
      />

      {/* Page content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {children || renderContent()}
      </main>

      {/* AI Assistant Chat */}
      <AIAssistantChat
        isOpen={showAIChat}
        onClose={() => setShowAIChat(false)}
        language={currentLanguage}
      />
    </div>
  );
};

export default AppLayout;
