'use client';

import { useState, ReactNode } from 'react';
import Dashboard from '../pages/Dashboard';
import Notes from '../pages/Notes';
import { Bot, Globe, User, Settings } from 'lucide-react';

interface AppLayoutProps {
  children?: ReactNode;
}

const AppLayout = ({ children }: AppLayoutProps) => {
  const [activeSection, setActiveSection] = useState('dashboard');

  const renderContent = () => {
    switch (activeSection) {
      case 'dashboard':
        return <Dashboard />;
      case 'notes':
        return <Notes />;
      case 'tasks':
        return <div className="text-center py-12"><h2 className="text-2xl font-bold text-white">Tasks - Coming Soon!</h2></div>;
      case 'content':
        return <div className="text-center py-12"><h2 className="text-2xl font-bold text-white">Content Creator - Coming Soon!</h2></div>;
      case 'calendar':
        return <div className="text-center py-12"><h2 className="text-2xl font-bold text-white">Calendar - Coming Soon!</h2></div>;
      default:
        return <div className="text-center py-12"><h2 className="text-2xl font-bold text-white">{activeSection} - Coming Soon!</h2></div>;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800">
      {/* New Header matching the screenshots */}
      <header className="bg-gradient-to-r from-blue-600 via-blue-700 to-blue-800 shadow-lg">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo and Title */}
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-orange-400 to-orange-500 rounded-full flex items-center justify-center shadow-lg">
                <span className="text-white font-bold text-lg">🐠</span>
              </div>
              <div>
                <h1 className="text-xl font-bold text-white">
                  Koi App
                </h1>
                <p className="text-blue-200 text-sm">
                  Your Joyful Habits Hub
                </p>
              </div>
            </div>

            {/* Right Navigation */}
            <div className="flex items-center space-x-4">
              <button className="flex items-center space-x-2 bg-blue-500/30 hover:bg-blue-500/50 px-3 py-2 rounded-lg transition-colors">
                <Bot className="w-4 h-4 text-white" />
                <span className="text-white text-sm font-medium">AI Assistant</span>
              </button>

              <button className="flex items-center space-x-2 bg-blue-500/30 hover:bg-blue-500/50 px-3 py-2 rounded-lg transition-colors">
                <Globe className="w-4 h-4 text-white" />
                <span className="text-white text-sm font-medium">EN</span>
              </button>

              <button className="flex items-center space-x-2 bg-blue-500/30 hover:bg-blue-500/50 px-3 py-2 rounded-lg transition-colors">
                <User className="w-4 h-4 text-white" />
                <span className="text-white text-sm font-medium">Account</span>
              </button>

              <button className="flex items-center space-x-2 bg-blue-500/30 hover:bg-blue-500/50 px-3 py-2 rounded-lg transition-colors">
                <Settings className="w-4 h-4 text-white" />
                <span className="text-white text-sm font-medium">Settings</span>
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Page content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {children || renderContent()}
      </main>
    </div>
  );
};

export default AppLayout;
