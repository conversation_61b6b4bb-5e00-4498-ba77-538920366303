'use client';

import {
  StickyNote,
  CheckSquare,
  Calendar,
  Lightbulb,
  Smile,
  Users2,
  Trophy,
  ChefHat,
  Gamepad2,
  Zap,
  Gift,
  Target,
  Clock,
  Mail,
  Phone,
  BookOpen,
  Heart,
  Globe,
  Quote,
  Utensils,
  Camera,
  BookText,
  Newspaper,
  Share,
  Award,
  Eye,
  Flame
} from 'lucide-react';
import Card from '../ui/Card';
import Button from '../ui/Button';

const Dashboard = () => {
  const productivityFeatures = [
    { id: 'notes', name: 'Notes', icon: StickyNote, description: 'Capture and organize your thoughts' },
    { id: 'tasks', name: 'Tasks', icon: CheckSquare, description: 'Manage your daily tasks and to-dos' },
    { id: 'calendar', name: 'Calendar', icon: Calendar, description: 'Schedule and track your events' },
    { id: 'email', name: 'Email', icon: Mail, description: 'Manage all your email accounts in one place' },
    { id: 'contacts', name: 'Contacts', icon: Phone, description: 'Keep track of important contacts' },
    { id: 'diary', name: 'Daily Diary', icon: BookO<PERSON>, description: 'Record your thoughts and feelings' },
    { id: 'health', name: 'Healthy Living', icon: Heart, description: 'Track workouts and nutrition' },
    { id: 'content', name: 'Content Creator', icon: Lightbulb, description: 'Create blogs, reports, essays and social media posts' },
    { id: 'websites', name: 'Favorite Websites', icon: Globe, description: 'Organize and access your favorite websites' },
  ];

  const funFeatures = [
    { id: 'facts', name: 'Fun Facts, Jokes, Quotes & Riddles', icon: Quote, description: 'Enjoy fun facts, jokes, quotes and riddles', color: 'text-pink-400' },
    { id: 'excuses', name: 'Excuses', icon: Smile, description: 'Generate customized excuses', color: 'text-purple-400' },
    { id: 'companions', name: 'Companions', icon: Users2, description: 'Manage your virtual companions', color: 'text-pink-400' },
    { id: 'achievements', name: 'My Achievements', icon: Trophy, description: 'View your badges, awards and trophies', color: 'text-pink-400' },
    { id: 'recipes', name: 'My Recipes', icon: Utensils, description: 'Create and organize your favorite recipes', color: 'text-purple-400' },
    { id: 'pictures', name: 'My Pictures', icon: Camera, description: 'Upload and collect your favorite pictures', color: 'text-pink-400' },
    { id: 'stories', name: 'Koi Adventure Stories', icon: BookText, description: 'Dive into interactive and imaginative Koi stories', color: 'text-pink-400' },
    { id: 'games', name: 'Mini-Games', icon: Gamepad2, description: 'Play quick and fun mini-games to boost your mood', color: 'text-purple-400' },
    { id: 'news', name: 'Latest News', icon: Newspaper, description: 'Catch up on the latest, family-friendly news', color: 'text-pink-400' },
  ];

  const gamingFeatures = [
    { id: 'level', name: 'Level Up', icon: Zap, description: 'Track your progress and level up' },
    { id: 'quests', name: 'Daily Quests', icon: Target, description: 'Complete challenges and earn rewards' },
    { id: 'rewards', name: 'Rewards', icon: Gift, description: 'Unlock achievements and collect rewards' },
    { id: 'leaderboard', name: 'Leaderboard', icon: Trophy, description: 'Compete with friends and climb ranks' },
    { id: 'badges', name: 'Badges', icon: Award, description: 'Earn badges for your accomplishments' },
    { id: 'streaks', name: 'Streaks', icon: Flame, description: 'Maintain daily activity streaks' },
  ];

  return (
    <div className="space-y-8">
      {/* Welcome Section */}
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold text-white mb-4">
          Welcome back! 👋
        </h1>
        <p className="text-blue-200 text-lg">
          Ready to make today amazing?
        </p>
      </div>

      {/* Dashboard Cards Row */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        {/* Your Progress Card */}
        <div className="bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl p-6 text-white shadow-lg">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Your Progress</h3>
            <Share className="w-5 h-5 text-blue-200" />
          </div>
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-10 h-10 bg-yellow-400 rounded-full flex items-center justify-center">
              <Zap className="w-5 h-5 text-yellow-900" />
            </div>
            <div>
              <p className="text-xl font-bold">Level 1</p>
              <p className="text-blue-200 text-sm">Novice Explorer</p>
            </div>
          </div>
          <div className="mb-2">
            <div className="flex justify-between text-sm mb-1">
              <span>41/100 XP to next level</span>
            </div>
            <div className="w-full bg-blue-400 rounded-full h-2">
              <div className="bg-yellow-400 h-2 rounded-full" style={{ width: '41%' }}></div>
            </div>
          </div>
        </div>

        {/* How are you feeling Card */}
        <div className="bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl p-6 text-white shadow-lg">
          <h3 className="text-lg font-semibold mb-4">How are you feeling?</h3>
          <div className="grid grid-cols-3 gap-3">
            <button className="bg-blue-400/50 hover:bg-blue-400/70 rounded-lg p-3 transition-colors">
              <span className="text-2xl">😊</span>
            </button>
            <button className="bg-blue-400/50 hover:bg-blue-400/70 rounded-lg p-3 transition-colors">
              <span className="text-2xl">😐</span>
            </button>
            <button className="bg-blue-400/50 hover:bg-blue-400/70 rounded-lg p-3 transition-colors">
              <span className="text-2xl">😔</span>
            </button>
            <button className="bg-blue-400/50 hover:bg-blue-400/70 rounded-lg p-3 transition-colors">
              <span className="text-2xl">😴</span>
            </button>
            <button className="bg-blue-400/50 hover:bg-blue-400/70 rounded-lg p-3 transition-colors">
              <span className="text-2xl">🤔</span>
            </button>
            <button className="bg-blue-400/50 hover:bg-blue-400/70 rounded-lg p-3 transition-colors">
              <span className="text-2xl">😎</span>
            </button>
          </div>
        </div>

        {/* Daily Inspiration Card */}
        <div className="bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl p-6 text-white shadow-lg">
          <h3 className="text-lg font-semibold mb-4">Daily Inspiration</h3>
          <div className="text-center">
            <Quote className="w-8 h-8 text-blue-200 mx-auto mb-3" />
            <p className="text-sm italic mb-3">
              "The journey of a thousand miles begins with one step."
            </p>
            <p className="text-xs text-blue-200">- Lao Tzu</p>
          </div>
        </div>
      </div>

      {/* Productivity Activities */}
      <div className="mb-8">
        <div className="mb-4">
          <h2 className="text-2xl font-bold text-white mb-2">Productivity Activities</h2>
          <p className="text-blue-200 text-sm">Tools to help you stay organized and efficient</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
          {productivityFeatures.map((feature) => (
            <button
              key={feature.id}
              className="bg-gradient-to-br from-blue-500/80 to-blue-600/80 hover:from-blue-400/80 hover:to-blue-500/80 rounded-xl p-4 text-left transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 group border border-blue-400/20"
            >
              <div className="flex items-center space-x-3 mb-2">
                <div className="w-8 h-8 flex items-center justify-center">
                  <feature.icon className="w-5 h-5 text-green-400 drop-shadow-[0_0_8px_rgba(34,197,94,0.8)] group-hover:drop-shadow-[0_0_12px_rgba(34,197,94,1)]" />
                </div>
                <h3 className="text-base font-semibold text-white">{feature.name}</h3>
              </div>
              <p className="text-blue-200 text-xs leading-relaxed">{feature.description}</p>
            </button>
          ))}
        </div>
      </div>

      {/* Fun Activities */}
      <div className="mb-8">
        <div className="mb-4">
          <h2 className="text-2xl font-bold text-white mb-2">Fun Activities</h2>
          <p className="text-blue-200 text-sm">Take a break and have some fun!</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
          {funFeatures.map((feature) => (
            <button
              key={feature.id}
              className="bg-gradient-to-br from-blue-500/80 to-blue-600/80 hover:from-blue-400/80 hover:to-blue-500/80 rounded-xl p-4 text-left transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 group border border-blue-400/20"
            >
              <div className="flex items-center space-x-3 mb-2">
                <div className="w-8 h-8 flex items-center justify-center">
                  <feature.icon className="w-5 h-5 text-pink-400 drop-shadow-[0_0_8px_rgba(244,114,182,0.8)] group-hover:drop-shadow-[0_0_12px_rgba(244,114,182,1)]" />
                </div>
                <h3 className="text-base font-semibold text-white">{feature.name}</h3>
              </div>
              <p className="text-blue-200 text-xs leading-relaxed">{feature.description}</p>
            </button>
          ))}
        </div>
      </div>

      {/* Gaming Activities */}
      <div className="mb-8">
        <div className="mb-4">
          <h2 className="text-2xl font-bold text-white mb-2">Gaming Activities</h2>
          <p className="text-blue-200 text-sm">Level up your life with gamification</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
          {gamingFeatures.map((feature) => (
            <button
              key={feature.id}
              className="bg-gradient-to-br from-blue-500/80 to-blue-600/80 hover:from-blue-400/80 hover:to-blue-500/80 rounded-xl p-4 text-left transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 group border border-blue-400/20"
            >
              <div className="flex items-center space-x-3 mb-2">
                <div className="w-8 h-8 flex items-center justify-center">
                  <feature.icon className="w-5 h-5 text-yellow-400 drop-shadow-[0_0_8px_rgba(250,204,21,0.8)] group-hover:drop-shadow-[0_0_12px_rgba(250,204,21,1)]" />
                </div>
                <h3 className="text-base font-semibold text-white">{feature.name}</h3>
              </div>
              <p className="text-blue-200 text-xs leading-relaxed">{feature.description}</p>
            </button>
          ))}
        </div>
      </div>

      {/* Achievements and Quests */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* Achievements */}
        <div className="bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl p-6 text-white shadow-lg">
          <h3 className="text-xl font-bold mb-4 flex items-center">
            <Award className="w-6 h-6 mr-3" />
            Achievements
          </h3>
          <p className="text-blue-200 text-sm mb-6">Complete challenges to earn achievements</p>
          <button className="w-full bg-white/20 hover:bg-white/30 py-3 rounded-lg font-medium transition-colors">
            View Collection
          </button>
        </div>

        {/* Current Quest */}
        <div className="bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl p-6 text-white shadow-lg">
          <h3 className="text-xl font-bold mb-4 flex items-center">
            <Target className="w-6 h-6 mr-3" />
            Current Quest
          </h3>
          <p className="text-blue-200 text-sm mb-6">Check your active quests and progress</p>
          <button className="w-full bg-white/20 hover:bg-white/30 py-3 rounded-lg font-medium transition-colors">
            View Quests
          </button>
        </div>
      </div>

      {/* Quest Progress */}
      <div className="mb-8">
        <div className="mb-6">
          <h2 className="text-2xl font-bold text-white mb-2">Quest Progress</h2>
          <p className="text-blue-200">Track your weekly and monthly quest completion</p>
        </div>

        {/* Weekly Quests */}
        <div className="bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl p-6 text-white shadow-lg mb-6">
          <div className="flex items-center space-x-3 mb-6">
            <div className="w-10 h-10 bg-yellow-400 rounded-lg flex items-center justify-center">
              <Zap className="w-5 h-5 text-yellow-900" />
            </div>
            <h3 className="text-xl font-bold">Weekly Quests</h3>
          </div>

          <div className="space-y-4">
            {/* Task Master Quest */}
            <div className="bg-blue-400/30 rounded-lg p-4">
              <div className="flex justify-between items-center mb-2">
                <div>
                  <h4 className="font-semibold">Task Master</h4>
                  <p className="text-blue-200 text-sm">Complete 15 tasks this week</p>
                </div>
                <span className="text-yellow-400 font-bold">+100 XP</span>
              </div>
              <div className="flex justify-between text-sm mb-2">
                <span>Progress</span>
                <span>0 / 15</span>
              </div>
              <div className="w-full bg-blue-300 rounded-full h-2">
                <div className="bg-yellow-400 h-2 rounded-full" style={{ width: '0%' }}></div>
              </div>
            </div>

            {/* Streak Champion Quest */}
            <div className="bg-blue-400/30 rounded-lg p-4">
              <div className="flex justify-between items-center mb-2">
                <div>
                  <h4 className="font-semibold">Streak Champion</h4>
                  <p className="text-blue-200 text-sm">Maintain a 7-day login streak</p>
                </div>
                <span className="text-yellow-400 font-bold">+150 XP</span>
              </div>
              <div className="flex justify-between text-sm mb-2">
                <span>Progress</span>
                <span>0 / 7</span>
              </div>
              <div className="w-full bg-blue-300 rounded-full h-2">
                <div className="bg-yellow-400 h-2 rounded-full" style={{ width: '0%' }}></div>
              </div>
            </div>
          </div>
        </div>

        {/* Monthly Quests */}
        <div className="bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl p-6 text-white shadow-lg">
          <div className="flex items-center space-x-3 mb-6">
            <div className="w-10 h-10 bg-yellow-400 rounded-lg flex items-center justify-center">
              <Calendar className="w-5 h-5 text-yellow-900" />
            </div>
            <h3 className="text-xl font-bold">Monthly Quests</h3>
          </div>

          <div className="space-y-4">
            {/* Productivity Pro Quest */}
            <div className="bg-blue-400/30 rounded-lg p-4">
              <div className="flex justify-between items-center mb-2">
                <div>
                  <h4 className="font-semibold">Productivity Pro</h4>
                  <p className="text-blue-200 text-sm">Complete 50 tasks this month</p>
                </div>
                <span className="text-yellow-400 font-bold">+500 XP</span>
              </div>
              <div className="flex justify-between text-sm mb-2">
                <span>Progress</span>
                <span>0 / 50</span>
              </div>
              <div className="w-full bg-blue-300 rounded-full h-2">
                <div className="bg-yellow-400 h-2 rounded-full" style={{ width: '0%' }}></div>
              </div>
            </div>

            {/* Dedication Master Quest */}
            <div className="bg-blue-400/30 rounded-lg p-4">
              <div className="flex justify-between items-center mb-2">
                <div>
                  <h4 className="font-semibold">Dedication Master</h4>
                  <p className="text-blue-200 text-sm">Log in for 30 days straight</p>
                </div>
                <span className="text-yellow-400 font-bold">+1000 XP</span>
              </div>
              <div className="flex justify-between text-sm mb-2">
                <span>Progress</span>
                <span>0 / 30</span>
              </div>
              <div className="w-full bg-blue-300 rounded-full h-2">
                <div className="bg-yellow-400 h-2 rounded-full" style={{ width: '0%' }}></div>
              </div>
            </div>
          </div>
        </div>
      </div>
      {/* Daily Progress Visualization */}
      <div className="mb-8">
        <div className="mb-6">
          <h2 className="text-2xl font-bold text-white mb-2">My Daily Progress Visualization</h2>
          <p className="text-blue-200">Watch as your garden grows, sky fills with stars, or river flows as you earn XP and level up!</p>
        </div>

        <div className="bg-gradient-to-br from-blue-400 via-blue-500 to-blue-600 rounded-2xl p-8 text-white shadow-lg relative overflow-hidden">
          {/* Sky and Sun */}
          <div className="absolute top-4 right-8">
            <div className="w-16 h-16 bg-yellow-400 rounded-full flex items-center justify-center shadow-lg">
              <span className="text-3xl">☀️</span>
            </div>
          </div>

          {/* Clouds */}
          <div className="absolute top-8 left-12">
            <div className="w-12 h-8 bg-white rounded-full opacity-80 flex items-center justify-center">
              <span className="text-sm">😊</span>
            </div>
          </div>
          <div className="absolute top-12 left-32">
            <div className="w-10 h-6 bg-white rounded-full opacity-60"></div>
          </div>

          {/* Island Scene */}
          <div className="relative z-10 text-center pt-16">
            <div className="inline-block relative">
              {/* Island Base */}
              <div className="w-48 h-24 bg-gradient-to-b from-orange-400 to-orange-500 rounded-full relative">
                {/* Island Top */}
                <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-2 w-32 h-16 bg-gradient-to-b from-green-400 to-green-500 rounded-full">
                  {/* Palm Tree */}
                  <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-4">
                    <div className="w-2 h-8 bg-amber-600"></div>
                    <div className="absolute -top-2 left-1/2 transform -translate-x-1/2">
                      <span className="text-2xl">🌴</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="mt-8">
              <p className="text-lg font-semibold mb-2">Island</p>
              <div className="max-w-md mx-auto">
                <div className="flex justify-between text-sm mb-2">
                  <span>Island Growth: 0%</span>
                </div>
                <div className="w-full bg-blue-300 rounded-full h-3">
                  <div className="bg-green-400 h-3 rounded-full" style={{ width: '0%' }}></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
