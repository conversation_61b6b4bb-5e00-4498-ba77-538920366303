'use client';

import { useState } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { useTranslation } from '../../lib/i18n/useTranslation';
import { Eye, EyeOff, Mail, Lock, Globe, ChevronDown, Fingerprint } from 'lucide-react';

interface AuthProps {
  language?: string;
  onLanguageChange?: (language: string) => void;
}

const Auth = ({ language = 'en', onLanguageChange }: AuthProps) => {
  const { t } = useTranslation(language as any);
  const { signUp, signIn, signInWithMagicLink, signInWithGoogle } = useAuth();
  
  const [authMode, setAuthMode] = useState<'signin' | 'signup' | 'magic'>('signin');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [showLanguageDropdown, setShowLanguageDropdown] = useState(false);

  const languages = [
    { code: 'en', label: 'US English', flag: '🇺🇸' },
    { code: 'es', label: 'ES Español', flag: '🇪🇸' },
    { code: 'fr', label: 'FR Français', flag: '🇫🇷' },
    { code: 'de', label: 'DE Deutsch', flag: '🇩🇪' },
    { code: 'it', label: 'IT Italiano', flag: '🇮🇹' },
    { code: 'pt', label: 'PT Português', flag: '🇵🇹' },
    { code: 'nl', label: 'NL Nederlands', flag: '🇳🇱' },
    { code: 'zh', label: 'CN 中文', flag: '🇨🇳' },
    { code: 'ja', label: 'JP 日本語', flag: '🇯🇵' },
    { code: 'ko', label: 'KR 한국어', flag: '🇰🇷' },
    { code: 'ru', label: 'RU Русский', flag: '🇷🇺' },
    { code: 'gr', label: 'GR Ελληνικά', flag: '🇬🇷' },
  ];

  const currentLang = languages.find(lang => lang.code === language) || languages[0];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setMessage('');

    try {
      let result;
      
      if (authMode === 'signup') {
        result = await signUp(email, password);
        if (!result.error) {
          setMessage('Check your email for verification link!');
        }
      } else if (authMode === 'signin') {
        result = await signIn(email, password);
      } else if (authMode === 'magic') {
        result = await signInWithMagicLink(email);
        if (!result.error) {
          setMessage('Check your email for the magic link!');
        }
      }

      if (result?.error) {
        setMessage(result.error.message);
      }
    } catch (error: any) {
      setMessage(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleSignIn = async () => {
    setLoading(true);
    try {
      const { error } = await signInWithGoogle();
      if (error) {
        setMessage(error.message);
      }
    } catch (error: any) {
      setMessage(error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800 flex items-center justify-center p-4">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(255,255,255,0.1),transparent_50%)]"></div>
      </div>

      <div className="relative w-full max-w-md">
        {/* Animated Koi Logo - Single bubble with proper Koi fish */}
        <div className="text-center mb-6">
          <div className="relative inline-block">
            {/* Single bubble with Koi fish - slow bobbing animation */}
            <div className="w-20 h-20 bg-gradient-to-br from-cyan-300 via-blue-400 to-purple-500 rounded-full flex items-center justify-center shadow-2xl mx-auto mb-4 animate-bounce border-4 border-white/20" style={{ animationDuration: '3s' }}>
              {/* Koi Fish SVG */}
              <svg width="48" height="48" viewBox="0 0 100 100" className="drop-shadow-lg">
                {/* Koi body - cyan to purple gradient */}
                <ellipse cx="40" cy="50" rx="25" ry="20" fill="url(#koiGradient)" />

                {/* Tail fin */}
                <path d="M15 50 Q5 40 10 30 Q15 35 20 40 Q15 45 10 50 Q15 55 20 60 Q15 65 10 70 Q5 60 15 50" fill="url(#finGradient)" />

                {/* Top fin */}
                <path d="M35 30 Q30 20 40 25 Q45 30 35 30" fill="url(#finGradient)" />

                {/* Side fins */}
                <ellipse cx="30" cy="60" rx="8" ry="4" fill="url(#finGradient)" transform="rotate(30 30 60)" />
                <ellipse cx="50" cy="60" rx="8" ry="4" fill="url(#finGradient)" transform="rotate(-30 50 60)" />

                {/* Eyes */}
                <circle cx="45" cy="45" r="4" fill="white" />
                <circle cx="45" cy="45" r="2.5" fill="black" />
                <circle cx="46" cy="44" r="1" fill="white" />

                {/* Spots */}
                <circle cx="35" cy="40" r="2" fill="#90EE90" opacity="0.8" />
                <circle cx="40" cy="35" r="1.5" fill="#90EE90" opacity="0.8" />
                <circle cx="30" cy="50" r="1.5" fill="#90EE90" opacity="0.8" />

                {/* Mouth */}
                <path d="M55 50 Q60 52 55 54" stroke="black" strokeWidth="1" fill="none" />

                {/* Gradients */}
                <defs>
                  <linearGradient id="koiGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" stopColor="#00FFFF" />
                    <stop offset="50%" stopColor="#0080FF" />
                    <stop offset="100%" stopColor="#8000FF" />
                  </linearGradient>
                  <linearGradient id="finGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" stopColor="#FF69B4" />
                    <stop offset="50%" stopColor="#DA70D6" />
                    <stop offset="100%" stopColor="#9370DB" />
                  </linearGradient>
                </defs>
              </svg>
            </div>
          </div>

          <h1 className="text-3xl font-bold text-white mb-1">
            Welcome to Koi App
          </h1>
          <p className="text-blue-200 text-base">
            Your personal productivity assistant
          </p>
        </div>

        {/* Auth Card */}
        <div className="bg-gradient-to-br from-blue-500/80 to-blue-600/80 backdrop-blur-lg rounded-3xl p-6 shadow-2xl border border-blue-400/30">
          {/* Language Selector */}
          <div className="relative mb-4">
            <button
              onClick={() => setShowLanguageDropdown(!showLanguageDropdown)}
              className="w-full flex items-center justify-center space-x-2 bg-blue-400/30 hover:bg-blue-400/50 px-3 py-2.5 rounded-xl transition-colors text-white"
            >
              <Globe className="w-4 h-4" />
              <span className="text-sm font-medium">{currentLang.flag} {currentLang.label}</span>
              <ChevronDown className="w-4 h-4" />
            </button>

            {showLanguageDropdown && (
              <div className="absolute top-full left-0 right-0 mt-2 bg-blue-600 rounded-xl shadow-lg border border-blue-400/30 z-50 max-h-60 overflow-y-auto">
                <div className="p-2">
                  {languages.map((lang) => (
                    <button
                      key={lang.code}
                      onClick={() => {
                        onLanguageChange?.(lang.code);
                        setShowLanguageDropdown(false);
                      }}
                      className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${
                        language === lang.code 
                          ? 'bg-blue-400/50 text-white' 
                          : 'text-blue-200 hover:bg-blue-500/30 hover:text-white'
                      }`}
                    >
                      <span className="text-lg">{lang.flag}</span>
                      <span className="text-sm font-medium">{lang.label}</span>
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Auth Mode Tabs */}
          <div className="flex bg-blue-600/50 rounded-2xl p-1 mb-4">
            <button
              onClick={() => setAuthMode('signin')}
              className={`flex-1 py-2.5 px-3 rounded-xl text-sm font-semibold transition-all ${
                authMode === 'signin'
                  ? 'bg-blue-400 text-white shadow-lg'
                  : 'text-blue-200 hover:text-white'
              }`}
            >
              Sign In
            </button>
            <button
              onClick={() => setAuthMode('signup')}
              className={`flex-1 py-2.5 px-3 rounded-xl text-sm font-semibold transition-all ${
                authMode === 'signup'
                  ? 'bg-blue-400 text-white shadow-lg'
                  : 'text-blue-200 hover:text-white'
              }`}
            >
              Sign Up
            </button>
            <button
              onClick={() => setAuthMode('magic')}
              className={`flex-1 py-2.5 px-3 rounded-xl text-sm font-semibold transition-all ${
                authMode === 'magic'
                  ? 'bg-blue-400 text-white shadow-lg'
                  : 'text-blue-200 hover:text-white'
              }`}
            >
              Magic Link
            </button>
          </div>

          {/* Auth Form */}
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Email Field */}
            <div>
              <label className="block text-blue-200 text-sm font-medium mb-1.5">
                Email
              </label>
              <div className="relative">
                <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-blue-300" />
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your email"
                  className="w-full bg-blue-700/50 text-white placeholder-blue-300 rounded-xl pl-10 pr-4 py-3 focus:outline-none focus:ring-2 focus:ring-cyan-400 border border-blue-500/30"
                  required
                />
              </div>
            </div>

            {/* Password Field (not shown for magic link) */}
            {authMode !== 'magic' && (
              <div>
                <div className="flex justify-between items-center mb-1.5">
                  <label className="text-blue-200 text-sm font-medium">
                    Password
                  </label>
                  {authMode === 'signin' && (
                    <button
                      type="button"
                      className="text-blue-300 text-xs hover:text-white transition-colors"
                    >
                      Forgot password?
                    </button>
                  )}
                </div>
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-blue-300" />
                  <input
                    type={showPassword ? 'text' : 'password'}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="••••••••"
                    className="w-full bg-blue-700/50 text-white placeholder-blue-300 rounded-xl pl-10 pr-10 py-3 focus:outline-none focus:ring-2 focus:ring-cyan-400 border border-blue-500/30"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-blue-300 hover:text-white transition-colors"
                  >
                    {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                  </button>
                </div>
              </div>
            )}

            {/* Submit Button */}
            <button
              type="submit"
              disabled={loading}
              className="w-full bg-gradient-to-r from-cyan-400 to-blue-500 hover:from-cyan-300 hover:to-blue-400 text-white font-semibold py-3 rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
            >
              {loading ? (
                <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              ) : (
                <>
                  <span>
                    {authMode === 'signin' ? 'Sign In' :
                     authMode === 'signup' ? 'Sign Up' :
                     'Send Magic Link'}
                  </span>
                  <span>→</span>
                </>
              )}
            </button>
          </form>

          {/* Google OAuth Button */}
          <div className="mt-4">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-blue-400/30"></div>
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-blue-600/80 text-blue-200">or</span>
              </div>
            </div>

            <button
              onClick={handleGoogleSignIn}
              disabled={loading}
              className="w-full mt-3 bg-white hover:bg-gray-50 text-gray-900 font-semibold py-3 rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-3"
            >
              <svg className="w-4 h-4" viewBox="0 0 24 24">
                <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
              </svg>
              <span>Continue with Google</span>
            </button>
          </div>

          {/* Message */}
          {message && (
            <div className={`mt-3 p-2.5 rounded-xl text-sm ${
              message.includes('Check your email')
                ? 'bg-green-500/20 text-green-200 border border-green-400/30'
                : 'bg-red-500/20 text-red-200 border border-red-400/30'
            }`}>
              {message}
            </div>
          )}
        </div>
      </div>

      {/* Click outside to close dropdown */}
      {showLanguageDropdown && (
        <div 
          className="fixed inset-0 z-40" 
          onClick={() => setShowLanguageDropdown(false)}
        />
      )}
    </div>
  );
};

export default Auth;
