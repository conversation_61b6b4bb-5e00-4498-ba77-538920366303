"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/pages/Dashboard.tsx":
/*!********************************************!*\
  !*** ./src/components/pages/Dashboard.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sticky-note.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/quote.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/smile.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users-round.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/utensils.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-text.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gamepad-2.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/newspaper.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gift.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst Dashboard = ()=>{\n    const productivityFeatures = [\n        {\n            id: 'notes',\n            name: 'Notes',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n            description: 'Capture and organize your thoughts'\n        },\n        {\n            id: 'tasks',\n            name: 'Tasks',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            description: 'Manage your daily tasks and to-dos'\n        },\n        {\n            id: 'calendar',\n            name: 'Calendar',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            description: 'Schedule and track your events'\n        },\n        {\n            id: 'email',\n            name: 'Email',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            description: 'Manage all your email accounts in one place'\n        },\n        {\n            id: 'contacts',\n            name: 'Contacts',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            description: 'Keep track of important contacts'\n        },\n        {\n            id: 'diary',\n            name: 'Daily Diary',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            description: 'Record your thoughts and feelings'\n        },\n        {\n            id: 'health',\n            name: 'Healthy Living',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            description: 'Track workouts and nutrition'\n        },\n        {\n            id: 'content',\n            name: 'Content Creator',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            description: 'Create blogs, reports, essays and social media posts'\n        },\n        {\n            id: 'websites',\n            name: 'Favorite Websites',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            description: 'Organize and access your favorite websites'\n        }\n    ];\n    const funFeatures = [\n        {\n            id: 'facts',\n            name: 'Fun Facts, Jokes, Quotes & Riddles',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            description: 'Enjoy fun facts, jokes, quotes and riddles',\n            color: 'text-pink-400'\n        },\n        {\n            id: 'excuses',\n            name: 'Excuses',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            description: 'Generate customized excuses',\n            color: 'text-purple-400'\n        },\n        {\n            id: 'companions',\n            name: 'Companions',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            description: 'Manage your virtual companions',\n            color: 'text-pink-400'\n        },\n        {\n            id: 'achievements',\n            name: 'My Achievements',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            description: 'View your badges, awards and trophies',\n            color: 'text-pink-400'\n        },\n        {\n            id: 'recipes',\n            name: 'My Recipes',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n            description: 'Create and organize your favorite recipes',\n            color: 'text-purple-400'\n        },\n        {\n            id: 'pictures',\n            name: 'My Pictures',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n            description: 'Upload and collect your favorite pictures',\n            color: 'text-pink-400'\n        },\n        {\n            id: 'stories',\n            name: 'Koi Adventure Stories',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n            description: 'Dive into interactive and imaginative Koi stories',\n            color: 'text-pink-400'\n        },\n        {\n            id: 'games',\n            name: 'Mini-Games',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n            description: 'Play quick and fun mini-games to boost your mood',\n            color: 'text-purple-400'\n        },\n        {\n            id: 'news',\n            name: 'Latest News',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n            description: 'Catch up on the latest, family-friendly news',\n            color: 'text-pink-400'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-4xl font-bold text-white mb-4\",\n                        children: \"Welcome back! \\uD83D\\uDC4B\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-blue-200 text-lg\",\n                        children: \"Ready to make today amazing?\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl p-6 text-white shadow-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold\",\n                                        children: \"Your Progress\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"w-5 h-5 text-blue-200\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-yellow-400 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            className: \"w-5 h-5 text-yellow-900\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xl font-bold\",\n                                                children: \"Level 1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                lineNumber: 84,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-200 text-sm\",\n                                                children: \"Novice Explorer\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                lineNumber: 85,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between text-sm mb-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"41/100 XP to next level\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full bg-blue-400 rounded-full h-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-yellow-400 h-2 rounded-full\",\n                                            style: {\n                                                width: '41%'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl p-6 text-white shadow-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold mb-4\",\n                                children: \"How are you feeling?\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-3 gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"bg-blue-400/50 hover:bg-blue-400/70 rounded-lg p-3 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl\",\n                                            children: \"\\uD83D\\uDE0A\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"bg-blue-400/50 hover:bg-blue-400/70 rounded-lg p-3 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl\",\n                                            children: \"\\uD83D\\uDE10\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"bg-blue-400/50 hover:bg-blue-400/70 rounded-lg p-3 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl\",\n                                            children: \"\\uD83D\\uDE14\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"bg-blue-400/50 hover:bg-blue-400/70 rounded-lg p-3 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl\",\n                                            children: \"\\uD83D\\uDE34\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"bg-blue-400/50 hover:bg-blue-400/70 rounded-lg p-3 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl\",\n                                            children: \"\\uD83E\\uDD14\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"bg-blue-400/50 hover:bg-blue-400/70 rounded-lg p-3 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl\",\n                                            children: \"\\uD83D\\uDE0E\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl p-6 text-white shadow-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold mb-4\",\n                                children: \"Daily Inspiration\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-8 h-8 text-blue-200 mx-auto mb-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm italic mb-3\",\n                                        children: '\"The journey of a thousand miles begins with one step.\"'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-blue-200\",\n                                        children: \"- Lao Tzu\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                lineNumber: 72,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-white mb-2\",\n                                children: \"Productivity Activities\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-200 text-sm\",\n                                children: \"Tools to help you stay organized and efficient\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-3\",\n                        children: productivityFeatures.map((feature)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"bg-gradient-to-br from-blue-500/80 to-blue-600/80 hover:from-blue-400/80 hover:to-blue-500/80 rounded-xl p-4 text-left transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 group border border-blue-400/20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                    className: \"w-5 h-5 text-green-400 drop-shadow-[0_0_8px_rgba(34,197,94,0.8)] group-hover:drop-shadow-[0_0_12px_rgba(34,197,94,1)]\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-base font-semibold text-white\",\n                                                children: feature.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-blue-200 text-xs leading-relaxed\",\n                                        children: feature.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, feature.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-white mb-2\",\n                                children: \"Fun Activities\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-200 text-sm\",\n                                children: \"Take a break and have some fun!\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-3\",\n                        children: funFeatures.map((feature)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"bg-gradient-to-br from-blue-500/80 to-blue-600/80 hover:from-blue-400/80 hover:to-blue-500/80 rounded-xl p-4 text-left transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 group border border-blue-400/20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                    className: \"w-5 h-5 text-pink-400 drop-shadow-[0_0_8px_rgba(244,114,182,0.8)] group-hover:drop-shadow-[0_0_12px_rgba(244,114,182,1)]\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-base font-semibold text-white\",\n                                                children: feature.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-blue-200 text-xs leading-relaxed\",\n                                        children: feature.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, feature.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                lineNumber: 162,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-white mb-2\",\n                                children: \"Gaming Activities\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-200 text-sm\",\n                                children: \"Level up your life with gamification\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-3\",\n                        children: gamingFeatures.map((feature)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"bg-gradient-to-br from-blue-500/80 to-blue-600/80 hover:from-blue-400/80 hover:to-blue-500/80 rounded-xl p-4 text-left transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 group border border-blue-400/20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                    className: \"w-5 h-5 text-yellow-400 drop-shadow-[0_0_8px_rgba(250,204,21,0.8)] group-hover:drop-shadow-[0_0_12px_rgba(250,204,21,1)]\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                    lineNumber: 201,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-base font-semibold text-white\",\n                                                children: feature.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-blue-200 text-xs leading-relaxed\",\n                                        children: feature.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, feature.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl p-6 text-white shadow-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-yellow-400 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"w-5 h-5 text-yellow-900\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold\",\n                                                children: \"Daily Check-In\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-blue-200 text-sm mb-4\",\n                                        children: \"Earn daily XP bonus\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"w-full bg-white/20 hover:bg-white/30 py-3 rounded-lg font-medium transition-colors\",\n                                        children: \"Check In Now\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl p-6 text-white shadow-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-purple-400 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"w-5 h-5 text-purple-900\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold\",\n                                                children: \"Mystery Box\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-blue-200 text-sm mb-4\",\n                                        children: \"Get random XP rewards\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"w-full bg-white/20 hover:bg-white/30 py-3 rounded-lg font-medium transition-colors\",\n                                        children: \"Open Mystery Box\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl p-6 text-white shadow-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-green-400 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"w-5 h-5 text-green-900\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold\",\n                                                children: \"Daily XP Boost\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-blue-200 text-sm mb-4\",\n                                        children: \"Complete tasks today for +20% bonus XP!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"w-full bg-white/20 hover:bg-white/30 py-3 rounded-lg font-medium transition-colors\",\n                                        children: \"Claim Boost\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                lineNumber: 187,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl p-6 text-white shadow-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold mb-4 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        className: \"w-6 h-6 mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Achievements\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-200 text-sm mb-6\",\n                                children: \"Complete challenges to earn achievements\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"w-full bg-white/20 hover:bg-white/30 py-3 rounded-lg font-medium transition-colors\",\n                                children: \"View Collection\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                        lineNumber: 259,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl p-6 text-white shadow-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold mb-4 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        className: \"w-6 h-6 mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Current Quest\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-200 text-sm mb-6\",\n                                children: \"Check your active quests and progress\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"w-full bg-white/20 hover:bg-white/30 py-3 rounded-lg font-medium transition-colors\",\n                                children: \"View Quests\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                        lineNumber: 271,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                lineNumber: 257,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-white mb-2\",\n                                children: \"Quest Progress\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-200\",\n                                children: \"Track your weekly and monthly quest completion\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 287,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                        lineNumber: 285,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl p-6 text-white shadow-lg mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-yellow-400 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            className: \"w-5 h-5 text-yellow-900\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                            lineNumber: 294,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-bold\",\n                                        children: \"Weekly Quests\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-400/30 rounded-lg p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold\",\n                                                                children: \"Task Master\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                                lineNumber: 304,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-blue-200 text-sm\",\n                                                                children: \"Complete 15 tasks this week\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                                lineNumber: 305,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                        lineNumber: 303,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-yellow-400 font-bold\",\n                                                        children: \"+100 XP\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                        lineNumber: 307,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between text-sm mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Progress\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                        lineNumber: 310,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"0 / 15\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                        lineNumber: 311,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full bg-blue-300 rounded-full h-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-yellow-400 h-2 rounded-full\",\n                                                    style: {\n                                                        width: '0%'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-400/30 rounded-lg p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold\",\n                                                                children: \"Streak Champion\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                                lineNumber: 322,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-blue-200 text-sm\",\n                                                                children: \"Maintain a 7-day login streak\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                                lineNumber: 323,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                        lineNumber: 321,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-yellow-400 font-bold\",\n                                                        children: \"+150 XP\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between text-sm mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Progress\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                        lineNumber: 328,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"0 / 7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                        lineNumber: 329,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full bg-blue-300 rounded-full h-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-yellow-400 h-2 rounded-full\",\n                                                    style: {\n                                                        width: '0%'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                    lineNumber: 332,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                        lineNumber: 291,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl p-6 text-white shadow-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-yellow-400 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"w-5 h-5 text-yellow-900\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-bold\",\n                                        children: \"Monthly Quests\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 344,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-400/30 rounded-lg p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold\",\n                                                                children: \"Productivity Pro\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                                lineNumber: 352,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-blue-200 text-sm\",\n                                                                children: \"Complete 50 tasks this month\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                                lineNumber: 353,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                        lineNumber: 351,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-yellow-400 font-bold\",\n                                                        children: \"+500 XP\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                        lineNumber: 355,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                lineNumber: 350,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between text-sm mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Progress\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                        lineNumber: 358,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"0 / 50\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                        lineNumber: 359,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                lineNumber: 357,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full bg-blue-300 rounded-full h-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-yellow-400 h-2 rounded-full\",\n                                                    style: {\n                                                        width: '0%'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                    lineNumber: 362,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                lineNumber: 361,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-400/30 rounded-lg p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold\",\n                                                                children: \"Dedication Master\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                                lineNumber: 370,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-blue-200 text-sm\",\n                                                                children: \"Log in for 30 days straight\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                                lineNumber: 371,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                        lineNumber: 369,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-yellow-400 font-bold\",\n                                                        children: \"+1000 XP\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                        lineNumber: 373,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between text-sm mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Progress\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                        lineNumber: 376,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"0 / 30\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                        lineNumber: 377,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                lineNumber: 375,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full bg-blue-300 rounded-full h-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-yellow-400 h-2 rounded-full\",\n                                                    style: {\n                                                        width: '0%'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                    lineNumber: 380,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                lineNumber: 379,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 367,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 347,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                        lineNumber: 339,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                lineNumber: 284,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-white mb-2\",\n                                children: \"My Daily Progress Visualization\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 389,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-200\",\n                                children: \"Watch as your garden grows, sky fills with stars, or river flows as you earn XP and level up!\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 390,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                        lineNumber: 388,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-br from-blue-400 via-blue-500 to-blue-600 rounded-2xl p-8 text-white shadow-lg relative overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-4 right-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-16 h-16 bg-yellow-400 rounded-full flex items-center justify-center shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-3xl\",\n                                        children: \"☀️\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                    lineNumber: 396,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 395,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-8 left-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-12 h-8 bg-white rounded-full opacity-80 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: \"\\uD83D\\uDE0A\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 404,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                    lineNumber: 403,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 402,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-12 left-32\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-6 bg-white rounded-full opacity-60\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                    lineNumber: 408,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 407,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative z-10 text-center pt-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-block relative\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-48 h-24 bg-gradient-to-b from-orange-400 to-orange-500 rounded-full relative\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-2 w-32 h-16 bg-gradient-to-b from-green-400 to-green-500 rounded-full\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-8 bg-amber-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                            lineNumber: 420,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute -top-2 left-1/2 transform -translate-x-1/2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-2xl\",\n                                                                children: \"\\uD83C\\uDF34\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                                lineNumber: 422,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                            lineNumber: 421,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                    lineNumber: 419,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                lineNumber: 417,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 413,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-lg font-semibold mb-2\",\n                                                children: \"Island\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                lineNumber: 430,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"max-w-md mx-auto\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-sm mb-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Island Growth: 0%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                            lineNumber: 433,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                        lineNumber: 432,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full bg-blue-300 rounded-full h-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-green-400 h-3 rounded-full\",\n                                                            style: {\n                                                                width: '0%'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                            lineNumber: 436,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                        lineNumber: 435,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                lineNumber: 431,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 429,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 412,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                        lineNumber: 393,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                lineNumber: 387,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n        lineNumber: 60,\n        columnNumber: 5\n    }, undefined);\n};\n_c = Dashboard;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Dashboard);\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/pages/Dashboard.tsx\n"));

/***/ })

});