"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_layout_AppLayout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/layout/AppLayout */ \"(app-pages-browser)/./src/components/layout/AppLayout.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction Home() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, loading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    // Redirect to auth page if not authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            if (!loading && !user) {\n                router.push('/auth');\n            }\n        }\n    }[\"Home.useEffect\"], [\n        user,\n        loading,\n        router\n    ]);\n    // Show loading while checking auth state\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 bg-gradient-to-br from-cyan-300 via-blue-400 to-purple-500 rounded-full flex items-center justify-center shadow-2xl mx-auto mb-6 animate-bounce border-2 border-white/20\",\n                        style: {\n                            animationDuration: '3s'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            width: \"40\",\n                            height: \"40\",\n                            viewBox: \"0 0 100 100\",\n                            className: \"drop-shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ellipse\", {\n                                    cx: \"40\",\n                                    cy: \"50\",\n                                    rx: \"25\",\n                                    ry: \"20\",\n                                    fill: \"url(#koiGradientMainPage)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 29,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M15 50 Q5 40 10 30 Q15 35 20 40 Q15 45 10 50 Q15 55 20 60 Q15 65 10 70 Q5 60 15 50\",\n                                    fill: \"url(#finGradientMainPage)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 32,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M35 30 Q30 20 40 25 Q45 30 35 30\",\n                                    fill: \"url(#finGradientMainPage)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 35,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ellipse\", {\n                                    cx: \"30\",\n                                    cy: \"60\",\n                                    rx: \"8\",\n                                    ry: \"4\",\n                                    fill: \"url(#finGradientMainPage)\",\n                                    transform: \"rotate(30 30 60)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ellipse\", {\n                                    cx: \"50\",\n                                    cy: \"60\",\n                                    rx: \"8\",\n                                    ry: \"4\",\n                                    fill: \"url(#finGradientMainPage)\",\n                                    transform: \"rotate(-30 50 60)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 39,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                    cx: \"45\",\n                                    cy: \"45\",\n                                    r: \"4\",\n                                    fill: \"white\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                    cx: \"45\",\n                                    cy: \"45\",\n                                    r: \"2.5\",\n                                    fill: \"black\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                    cx: \"46\",\n                                    cy: \"44\",\n                                    r: \"1\",\n                                    fill: \"white\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 44,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                    cx: \"35\",\n                                    cy: \"40\",\n                                    r: \"2\",\n                                    fill: \"#90EE90\",\n                                    opacity: \"0.8\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                    cx: \"40\",\n                                    cy: \"35\",\n                                    r: \"1.5\",\n                                    fill: \"#90EE90\",\n                                    opacity: \"0.8\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                    cx: \"30\",\n                                    cy: \"50\",\n                                    r: \"1.5\",\n                                    fill: \"#90EE90\",\n                                    opacity: \"0.8\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M55 50 Q60 52 55 54\",\n                                    stroke: \"black\",\n                                    strokeWidth: \"1\",\n                                    fill: \"none\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                                            id: \"koiGradientMainPage\",\n                                            x1: \"0%\",\n                                            y1: \"0%\",\n                                            x2: \"100%\",\n                                            y2: \"100%\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                    offset: \"0%\",\n                                                    stopColor: \"#00FFFF\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 57,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                    offset: \"50%\",\n                                                    stopColor: \"#0080FF\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 58,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                    offset: \"100%\",\n                                                    stopColor: \"#8000FF\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 59,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 56,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                                            id: \"finGradientMainPage\",\n                                            x1: \"0%\",\n                                            y1: \"0%\",\n                                            x2: \"100%\",\n                                            y2: \"100%\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                    offset: \"0%\",\n                                                    stopColor: \"#FF69B4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 62,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                    offset: \"50%\",\n                                                    stopColor: \"#DA70D6\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 63,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                    offset: \"100%\",\n                                                    stopColor: \"#9370DB\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 64,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 27,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-white mb-4\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 23,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, this);\n    }\n    // Don't render app if user is not authenticated\n    if (!user) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_AppLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 83,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"ECuTCuyBzuCScSXDCsKsZ4/7zwU=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});