"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/pages/Dashboard.tsx":
/*!********************************************!*\
  !*** ./src/components/pages/Dashboard.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sticky-note.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/quote.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/smile.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users-round.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/utensils.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-text.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gamepad-2.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/newspaper.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gift.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/flame.js\");\n/* harmony import */ var _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Award,BookOpen,BookText,Calendar,Camera,CheckSquare,Flame,Gamepad2,Gift,Globe,Heart,Lightbulb,Mail,Newspaper,Phone,Quote,Share,Smile,StickyNote,Target,Trophy,Users2,Utensils,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share.js\");\n/* harmony import */ var _lib_i18n_useTranslation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../lib/i18n/useTranslation */ \"(app-pages-browser)/./src/lib/i18n/useTranslation.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst Dashboard = (param)=>{\n    let { language = 'en' } = param;\n    _s();\n    const { t } = (0,_lib_i18n_useTranslation__WEBPACK_IMPORTED_MODULE_1__.useTranslation)(language);\n    const productivityFeatures = [\n        {\n            id: 'notes',\n            name: t('notes'),\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            description: t('notesDesc')\n        },\n        {\n            id: 'tasks',\n            name: t('tasks'),\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            description: t('tasksDesc')\n        },\n        {\n            id: 'calendar',\n            name: t('calendar'),\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            description: t('calendarDesc')\n        },\n        {\n            id: 'email',\n            name: t('email'),\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            description: t('emailDesc')\n        },\n        {\n            id: 'contacts',\n            name: t('contacts'),\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            description: t('contactsDesc')\n        },\n        {\n            id: 'diary',\n            name: t('diary'),\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            description: t('diaryDesc')\n        },\n        {\n            id: 'health',\n            name: t('health'),\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            description: t('healthDesc')\n        },\n        {\n            id: 'content',\n            name: t('content'),\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            description: t('contentDesc')\n        },\n        {\n            id: 'websites',\n            name: t('websites'),\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            description: t('websitesDesc')\n        }\n    ];\n    const funFeatures = [\n        {\n            id: 'facts',\n            name: 'Fun Facts, Jokes, Quotes & Riddles',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            description: 'Enjoy fun facts, jokes, quotes and riddles',\n            color: 'text-pink-400'\n        },\n        {\n            id: 'excuses',\n            name: 'Excuses',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            description: 'Generate customized excuses',\n            color: 'text-purple-400'\n        },\n        {\n            id: 'companions',\n            name: 'Companions',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            description: 'Manage your virtual companions',\n            color: 'text-pink-400'\n        },\n        {\n            id: 'achievements',\n            name: 'My Achievements',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n            description: 'View your badges, awards and trophies',\n            color: 'text-pink-400'\n        },\n        {\n            id: 'recipes',\n            name: 'My Recipes',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n            description: 'Create and organize your favorite recipes',\n            color: 'text-purple-400'\n        },\n        {\n            id: 'pictures',\n            name: 'My Pictures',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n            description: 'Upload and collect your favorite pictures',\n            color: 'text-pink-400'\n        },\n        {\n            id: 'stories',\n            name: 'Koi Adventure Stories',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n            description: 'Dive into interactive and imaginative Koi stories',\n            color: 'text-pink-400'\n        },\n        {\n            id: 'games',\n            name: 'Mini-Games',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n            description: 'Play quick and fun mini-games to boost your mood',\n            color: 'text-purple-400'\n        },\n        {\n            id: 'news',\n            name: 'Latest News',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n            description: 'Catch up on the latest, family-friendly news',\n            color: 'text-pink-400'\n        }\n    ];\n    const gamingFeatures = [\n        {\n            id: 'level',\n            name: 'Level Up',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n            description: 'Track your progress and level up'\n        },\n        {\n            id: 'quests',\n            name: 'Daily Quests',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n            description: 'Complete challenges and earn rewards'\n        },\n        {\n            id: 'rewards',\n            name: 'Rewards',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n            description: 'Unlock achievements and collect rewards'\n        },\n        {\n            id: 'leaderboard',\n            name: 'Leaderboard',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n            description: 'Compete with friends and climb ranks'\n        },\n        {\n            id: 'badges',\n            name: 'Badges',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n            description: 'Earn badges for your accomplishments'\n        },\n        {\n            id: 'streaks',\n            name: 'Streaks',\n            icon: _barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"],\n            description: 'Maintain daily activity streaks'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-4xl font-bold text-white mb-4\",\n                        children: \"Welcome back! \\uD83D\\uDC4B\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-blue-200 text-lg\",\n                        children: \"Ready to make today amazing?\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                lineNumber: 79,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl p-6 text-white shadow-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold\",\n                                        children: \"Your Progress\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                        className: \"w-5 h-5 text-blue-200\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-yellow-400 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            className: \"w-5 h-5 text-yellow-900\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xl font-bold\",\n                                                children: \"Level 1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-200 text-sm\",\n                                                children: \"Novice Explorer\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between text-sm mb-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"41/100 XP to next level\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full bg-blue-400 rounded-full h-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-yellow-400 h-2 rounded-full\",\n                                            style: {\n                                                width: '41%'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl p-6 text-white shadow-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold mb-4\",\n                                children: \"How are you feeling?\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-3 gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"bg-blue-400/50 hover:bg-blue-400/70 rounded-lg p-3 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl\",\n                                            children: \"\\uD83D\\uDE0A\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"bg-blue-400/50 hover:bg-blue-400/70 rounded-lg p-3 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl\",\n                                            children: \"\\uD83D\\uDE10\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"bg-blue-400/50 hover:bg-blue-400/70 rounded-lg p-3 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl\",\n                                            children: \"\\uD83D\\uDE14\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"bg-blue-400/50 hover:bg-blue-400/70 rounded-lg p-3 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl\",\n                                            children: \"\\uD83D\\uDE34\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"bg-blue-400/50 hover:bg-blue-400/70 rounded-lg p-3 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl\",\n                                            children: \"\\uD83E\\uDD14\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"bg-blue-400/50 hover:bg-blue-400/70 rounded-lg p-3 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl\",\n                                            children: \"\\uD83D\\uDE0E\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl p-6 text-white shadow-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold mb-4\",\n                                children: \"Daily Inspiration\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_BookOpen_BookText_Calendar_Camera_CheckSquare_Flame_Gamepad2_Gift_Globe_Heart_Lightbulb_Mail_Newspaper_Phone_Quote_Share_Smile_StickyNote_Target_Trophy_Users2_Utensils_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-8 h-8 text-blue-200 mx-auto mb-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm italic mb-3\",\n                                        children: '\"The journey of a thousand miles begins with one step.\"'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-blue-200\",\n                                        children: \"- Lao Tzu\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-white mb-2\",\n                                children: \"Productivity Activities\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-200 text-sm\",\n                                children: \"Tools to help you stay organized and efficient\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-3\",\n                        children: productivityFeatures.map((feature)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"bg-gradient-to-br from-blue-500/80 to-blue-600/80 hover:from-blue-400/80 hover:to-blue-500/80 rounded-xl p-4 text-left transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 group border border-blue-400/20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                    className: \"w-5 h-5 text-green-400 drop-shadow-[0_0_8px_rgba(34,197,94,0.8)] group-hover:drop-shadow-[0_0_12px_rgba(34,197,94,1)]\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-base font-semibold text-white\",\n                                                children: feature.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-blue-200 text-xs leading-relaxed\",\n                                        children: feature.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, feature.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                lineNumber: 154,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-white mb-2\",\n                                children: \"Fun Activities\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-200 text-sm\",\n                                children: \"Take a break and have some fun!\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-3\",\n                        children: funFeatures.map((feature)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"bg-gradient-to-br from-blue-500/80 to-blue-600/80 hover:from-blue-400/80 hover:to-blue-500/80 rounded-xl p-4 text-left transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 group border border-blue-400/20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                    className: \"w-5 h-5 text-pink-400 drop-shadow-[0_0_8px_rgba(244,114,182,0.8)] group-hover:drop-shadow-[0_0_12px_rgba(244,114,182,1)]\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                lineNumber: 192,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-base font-semibold text-white\",\n                                                children: feature.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-blue-200 text-xs leading-relaxed\",\n                                        children: feature.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, feature.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                lineNumber: 179,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-white mb-2\",\n                                children: \"Gaming Activities\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-200 text-sm\",\n                                children: \"Level up your life with gamification\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-3\",\n                        children: gamingFeatures.map((feature)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"bg-gradient-to-br from-blue-500/80 to-blue-600/80 hover:from-blue-400/80 hover:to-blue-500/80 rounded-xl p-4 text-left transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 group border border-blue-400/20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                    className: \"w-5 h-5 text-yellow-400 drop-shadow-[0_0_8px_rgba(250,204,21,0.8)] group-hover:drop-shadow-[0_0_12px_rgba(250,204,21,1)]\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-base font-semibold text-white\",\n                                                children: feature.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-blue-200 text-xs leading-relaxed\",\n                                        children: feature.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, feature.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                lineNumber: 204,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-white mb-2\",\n                                children: \"My Daily Progress Visualization\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-200\",\n                                children: \"Watch as your garden grows, sky fills with stars, or river flows as you earn XP and level up!\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-br from-blue-400 via-blue-500 to-blue-600 rounded-2xl p-8 text-white shadow-lg relative overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-4 right-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-16 h-16 bg-yellow-400 rounded-full flex items-center justify-center shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-3xl\",\n                                        children: \"☀️\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-8 left-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-12 h-8 bg-white rounded-full opacity-80 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: \"\\uD83D\\uDE0A\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-12 left-32\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-6 bg-white rounded-full opacity-60\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative z-10 text-center pt-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-block relative\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-48 h-24 bg-gradient-to-b from-orange-400 to-orange-500 rounded-full relative\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-2 w-32 h-16 bg-gradient-to-b from-green-400 to-green-500 rounded-full\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-8 bg-amber-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                            lineNumber: 264,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute -top-2 left-1/2 transform -translate-x-1/2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-2xl\",\n                                                                children: \"\\uD83C\\uDF34\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                                lineNumber: 266,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                            lineNumber: 265,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-lg font-semibold mb-2\",\n                                                children: \"Island\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"max-w-md mx-auto\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-sm mb-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Island Growth: 0%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                            lineNumber: 277,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                        lineNumber: 276,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full bg-blue-300 rounded-full h-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-green-400 h-3 rounded-full\",\n                                                            style: {\n                                                                width: '0%'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                            lineNumber: 280,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n                lineNumber: 231,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Dashboard.tsx\",\n        lineNumber: 77,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Dashboard, \"zlIdU9EjM2llFt74AbE2KsUJXyM=\", false, function() {\n    return [\n        _lib_i18n_useTranslation__WEBPACK_IMPORTED_MODULE_1__.useTranslation\n    ];\n});\n_c = Dashboard;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Dashboard);\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/pages/Dashboard.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/i18n/translations.ts":
/*!**************************************!*\
  !*** ./src/lib/i18n/translations.ts ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   translations: () => (/* binding */ translations)\n/* harmony export */ });\nconst translations = {\n    en: {\n        // Header\n        appName: 'Koi App',\n        appSubtitle: 'Your Ultimate AI Productivity Assistant',\n        aiAssistant: 'AI Assistant',\n        account: 'Account',\n        signOut: 'Sign Out',\n        // Dashboard\n        welcomeBack: 'Welcome back! 👋',\n        readyToday: 'Ready to make today amazing?',\n        yourProgress: 'Your Progress',\n        level: 'Level',\n        noviceExplorer: 'Novice Explorer',\n        xpToNextLevel: 'XP to next level',\n        howAreYouFeeling: 'How are you feeling?',\n        dailyInspiration: 'Daily Inspiration',\n        // Activities\n        productivityActivities: 'Productivity Activities',\n        productivityDescription: 'Tools to help you stay organized and efficient',\n        funActivities: 'Fun Activities',\n        funDescription: 'Take a break and have some fun!',\n        gamingActivities: 'Gaming Activities',\n        gamingDescription: 'Level up your life with gamification',\n        // Features\n        notes: 'Notes',\n        notesDesc: 'Capture and organize your thoughts',\n        tasks: 'Tasks',\n        tasksDesc: 'Manage your daily tasks and to-dos',\n        calendar: 'Calendar',\n        calendarDesc: 'Schedule and track your events',\n        email: 'Email',\n        emailDesc: 'Manage all your email accounts in one place',\n        contacts: 'Contacts',\n        contactsDesc: 'Keep track of important contacts',\n        diary: 'Daily Diary',\n        diaryDesc: 'Record your thoughts and feelings',\n        health: 'Healthy Living',\n        healthDesc: 'Track workouts and nutrition',\n        content: 'Content Creator',\n        contentDesc: 'Create blogs, reports, essays and social media posts',\n        websites: 'Favorite Websites',\n        websitesDesc: 'Organize and access your favorite websites',\n        // Gaming Features\n        levelUp: 'Level Up',\n        levelUpDesc: 'Track your progress and level up',\n        dailyQuests: 'Daily Quests',\n        dailyQuestsDesc: 'Complete challenges and earn rewards',\n        rewards: 'Rewards',\n        rewardsDesc: 'Unlock achievements and collect rewards',\n        leaderboard: 'Leaderboard',\n        leaderboardDesc: 'Compete with friends and climb ranks',\n        badges: 'Badges',\n        badgesDesc: 'Earn badges for your accomplishments',\n        streaks: 'Streaks',\n        streaksDesc: 'Maintain daily activity streaks',\n        // Settings\n        settings: 'Settings',\n        settingsDesc: 'Customize your Koi app experience',\n        profileSettings: 'Profile Settings',\n        profileDesc: 'Manage your account and personal information',\n        notifications: 'Notifications',\n        notificationsDesc: 'Configure alerts and reminders',\n        privacy: 'Privacy & Security',\n        privacyDesc: 'Control your data and security settings',\n        appearance: 'Appearance',\n        appearanceDesc: 'Customize themes and display options',\n        languageRegion: 'Language & Region',\n        languageDesc: 'Set your preferred language and locale',\n        dataManagement: 'Data Management',\n        dataDesc: 'Import, export, and backup your data',\n        helpSupport: 'Help & Support',\n        helpDesc: 'Get help and contact support',\n        aboutApp: 'About Koi App',\n        aboutDesc: 'Version info and legal information',\n        // AI Assistant\n        aiChatTitle: 'AI Assistant',\n        aiChatSubtitle: 'Always here to help',\n        typeMessage: 'Type your message...',\n        aiWelcome: 'Hello! I\\'m your AI assistant. How can I help you today?',\n        // Common\n        comingSoon: 'Coming Soon',\n        back: 'Back',\n        save: 'Save',\n        cancel: 'Cancel',\n        close: 'Close',\n        send: 'Send'\n    },\n    es: {\n        // Header\n        appName: 'Koi App',\n        appSubtitle: 'Tu Asistente de Productividad AI Definitivo',\n        aiAssistant: 'Asistente IA',\n        account: 'Cuenta',\n        signOut: 'Cerrar Sesión',\n        // Dashboard\n        welcomeBack: '¡Bienvenido de vuelta! 👋',\n        readyToday: '¿Listo para hacer hoy increíble?',\n        yourProgress: 'Tu Progreso',\n        level: 'Nivel',\n        noviceExplorer: 'Explorador Novato',\n        xpToNextLevel: 'XP al siguiente nivel',\n        howAreYouFeeling: '¿Cómo te sientes?',\n        dailyInspiration: 'Inspiración Diaria',\n        // Activities\n        productivityActivities: 'Actividades de Productividad',\n        productivityDescription: 'Herramientas para mantenerte organizado y eficiente',\n        funActivities: 'Actividades Divertidas',\n        funDescription: '¡Tómate un descanso y diviértete!',\n        gamingActivities: 'Actividades de Juego',\n        gamingDescription: 'Sube de nivel tu vida con gamificación',\n        // Features\n        notes: 'Notas',\n        notesDesc: 'Captura y organiza tus pensamientos',\n        tasks: 'Tareas',\n        tasksDesc: 'Gestiona tus tareas diarias y pendientes',\n        calendar: 'Calendario',\n        calendarDesc: 'Programa y rastrea tus eventos',\n        email: 'Correo',\n        emailDesc: 'Gestiona todas tus cuentas de correo en un lugar',\n        contacts: 'Contactos',\n        contactsDesc: 'Mantén registro de contactos importantes',\n        diary: 'Diario Personal',\n        diaryDesc: 'Registra tus pensamientos y sentimientos',\n        health: 'Vida Saludable',\n        healthDesc: 'Rastrea ejercicios y nutrición',\n        content: 'Creador de Contenido',\n        contentDesc: 'Crea blogs, informes, ensayos y publicaciones sociales',\n        websites: 'Sitios Web Favoritos',\n        websitesDesc: 'Organiza y accede a tus sitios web favoritos',\n        // Gaming Features\n        levelUp: 'Subir Nivel',\n        levelUpDesc: 'Rastrea tu progreso y sube de nivel',\n        dailyQuests: 'Misiones Diarias',\n        dailyQuestsDesc: 'Completa desafíos y gana recompensas',\n        rewards: 'Recompensas',\n        rewardsDesc: 'Desbloquea logros y colecciona recompensas',\n        leaderboard: 'Tabla de Líderes',\n        leaderboardDesc: 'Compite con amigos y escala posiciones',\n        badges: 'Insignias',\n        badgesDesc: 'Gana insignias por tus logros',\n        streaks: 'Rachas',\n        streaksDesc: 'Mantén rachas de actividad diaria',\n        // Settings\n        settings: 'Configuración',\n        settingsDesc: 'Personaliza tu experiencia con Koi app',\n        profileSettings: 'Configuración de Perfil',\n        profileDesc: 'Gestiona tu cuenta e información personal',\n        notifications: 'Notificaciones',\n        notificationsDesc: 'Configura alertas y recordatorios',\n        privacy: 'Privacidad y Seguridad',\n        privacyDesc: 'Controla tus datos y configuración de seguridad',\n        appearance: 'Apariencia',\n        appearanceDesc: 'Personaliza temas y opciones de visualización',\n        languageRegion: 'Idioma y Región',\n        languageDesc: 'Establece tu idioma y configuración regional preferidos',\n        dataManagement: 'Gestión de Datos',\n        dataDesc: 'Importa, exporta y respalda tus datos',\n        helpSupport: 'Ayuda y Soporte',\n        helpDesc: 'Obtén ayuda y contacta soporte',\n        aboutApp: 'Acerca de Koi App',\n        aboutDesc: 'Información de versión e información legal',\n        // AI Assistant\n        aiChatTitle: 'Asistente IA',\n        aiChatSubtitle: 'Siempre aquí para ayudar',\n        typeMessage: 'Escribe tu mensaje...',\n        aiWelcome: '¡Hola! Soy tu asistente IA. ¿Cómo puedo ayudarte hoy?',\n        // Common\n        comingSoon: 'Próximamente',\n        back: 'Atrás',\n        save: 'Guardar',\n        cancel: 'Cancelar',\n        close: 'Cerrar',\n        send: 'Enviar'\n    },\n    fr: {\n        // Header\n        appName: 'Koi App',\n        appSubtitle: 'Votre Assistant de Productivité IA Ultime',\n        aiAssistant: 'Assistant IA',\n        account: 'Compte',\n        signOut: 'Se Déconnecter',\n        // Dashboard\n        welcomeBack: 'Bon retour ! 👋',\n        readyToday: 'Prêt à rendre aujourd\\'hui incroyable ?',\n        yourProgress: 'Votre Progrès',\n        level: 'Niveau',\n        noviceExplorer: 'Explorateur Novice',\n        xpToNextLevel: 'XP au niveau suivant',\n        howAreYouFeeling: 'Comment vous sentez-vous ?',\n        dailyInspiration: 'Inspiration Quotidienne',\n        // Activities\n        productivityActivities: 'Activités de Productivité',\n        productivityDescription: 'Outils pour vous aider à rester organisé et efficace',\n        funActivities: 'Activités Amusantes',\n        funDescription: 'Prenez une pause et amusez-vous !',\n        gamingActivities: 'Activités de Jeu',\n        gamingDescription: 'Améliorez votre vie avec la gamification',\n        // Common\n        comingSoon: 'Bientôt Disponible',\n        back: 'Retour',\n        save: 'Sauvegarder',\n        cancel: 'Annuler',\n        close: 'Fermer',\n        send: 'Envoyer'\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/i18n/translations.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/i18n/useTranslation.ts":
/*!****************************************!*\
  !*** ./src/lib/i18n/useTranslation.ts ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getLanguageLabel: () => (/* binding */ getLanguageLabel),\n/* harmony export */   useTranslation: () => (/* binding */ useTranslation)\n/* harmony export */ });\n/* harmony import */ var _translations__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./translations */ \"(app-pages-browser)/./src/lib/i18n/translations.ts\");\n\nconst useTranslation = function() {\n    let language = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 'en';\n    const t = (key)=>{\n        var _translations_language;\n        const translation = (_translations_language = _translations__WEBPACK_IMPORTED_MODULE_0__.translations[language]) === null || _translations_language === void 0 ? void 0 : _translations_language[key];\n        if (translation) {\n            return translation;\n        }\n        // Fallback to English if translation not found\n        const fallback = _translations__WEBPACK_IMPORTED_MODULE_0__.translations.en[key];\n        if (fallback) {\n            return fallback;\n        }\n        // Return the key if no translation found\n        return key;\n    };\n    return {\n        t\n    };\n};\nconst getLanguageLabel = (code)=>{\n    const languageLabels = {\n        en: 'US English',\n        es: 'ES Español',\n        fr: 'FR Français',\n        de: 'DE Deutsch',\n        it: 'IT Italiano',\n        pt: 'PT Português',\n        nl: 'NL Nederlands',\n        zh: 'CN 中文',\n        ja: 'JP 日本語',\n        ko: 'KR 한국어',\n        ru: 'RU Русский',\n        gr: 'GR Ελληνικά',\n        he: 'IL עברית',\n        th: 'TH ไทย',\n        vi: 'VN Tiếng Việt',\n        id: 'ID Bahasa Indonesia'\n    };\n    return languageLabels[code] || code.toUpperCase();\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/i18n/useTranslation.ts\n"));

/***/ })

});