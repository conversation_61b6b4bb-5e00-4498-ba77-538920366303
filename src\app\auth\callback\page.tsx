'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { supabase } from '../../../lib/supabase/client';

export default function AuthCallback() {
  const router = useRouter();

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        const { data, error } = await supabase.auth.getSession();
        
        if (error) {
          console.error('Auth callback error:', error);
          router.push('/auth?error=' + encodeURIComponent(error.message));
          return;
        }

        if (data.session) {
          // Successfully authenticated, redirect to dashboard
          router.push('/');
        } else {
          // No session, redirect to auth page
          router.push('/auth');
        }
      } catch (error) {
        console.error('Unexpected error in auth callback:', error);
        router.push('/auth?error=unexpected_error');
      }
    };

    handleAuthCallback();
  }, [router]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800 flex items-center justify-center">
      <div className="text-center">
        {/* Animated Koi Logo */}
        <div className="w-16 h-16 bg-gradient-to-br from-cyan-400 via-blue-500 to-purple-600 rounded-full flex items-center justify-center shadow-2xl animate-pulse mx-auto mb-6">
          <div className="w-14 h-14 bg-gradient-to-br from-cyan-300 to-blue-400 rounded-full flex items-center justify-center animate-bounce" style={{ animationDuration: '3s' }}>
            <span className="text-white font-bold text-2xl">🐠</span>
          </div>
        </div>
        
        <h2 className="text-2xl font-bold text-white mb-4">Authenticating...</h2>
        <p className="text-blue-200">Please wait while we sign you in.</p>
        
        {/* Loading spinner */}
        <div className="mt-6">
          <div className="w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin mx-auto"></div>
        </div>
      </div>
    </div>
  );
}
