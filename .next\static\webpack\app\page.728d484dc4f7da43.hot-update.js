"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js":
/*!******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/chevron-down.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ ChevronDown)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"m6 9 6 6 6-6\",\n            key: \"qrunsl\"\n        }\n    ]\n];\nconst ChevronDown = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"chevron-down\", __iconNode);\n //# sourceMappingURL=chevron-down.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvY2hldnJvbi1kb3duLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUdhLG1CQUF1QjtJQUFDO1FBQUMsTUFBUTtRQUFBO1lBQUUsR0FBRyxjQUFnQjtZQUFBLElBQUssU0FBUztRQUFBLENBQUM7S0FBQztDQUFBO0FBYTdFLGtCQUFjLGtFQUFpQixpQkFBZ0IsQ0FBVSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxjaGlja1xcc3JjXFxpY29uc1xcY2hldnJvbi1kb3duLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24nO1xuaW1wb3J0IHsgSWNvbk5vZGUgfSBmcm9tICcuLi90eXBlcyc7XG5cbmV4cG9ydCBjb25zdCBfX2ljb25Ob2RlOiBJY29uTm9kZSA9IFtbJ3BhdGgnLCB7IGQ6ICdtNiA5IDYgNiA2LTYnLCBrZXk6ICdxcnVuc2wnIH1dXTtcblxuLyoqXG4gKiBAY29tcG9uZW50IEBuYW1lIENoZXZyb25Eb3duXG4gKiBAZGVzY3JpcHRpb24gTHVjaWRlIFNWRyBpY29uIGNvbXBvbmVudCwgcmVuZGVycyBTVkcgRWxlbWVudCB3aXRoIGNoaWxkcmVuLlxuICpcbiAqIEBwcmV2aWV3ICFbaW1nXShkYXRhOmltYWdlL3N2Zyt4bWw7YmFzZTY0LFBITjJaeUFnZUcxc2JuTTlJbWgwZEhBNkx5OTNkM2N1ZHpNdWIzSm5Mekl3TURBdmMzWm5JZ29nSUhkcFpIUm9QU0l5TkNJS0lDQm9aV2xuYUhROUlqSTBJZ29nSUhacFpYZENiM2c5SWpBZ01DQXlOQ0F5TkNJS0lDQm1hV3hzUFNKdWIyNWxJZ29nSUhOMGNtOXJaVDBpSXpBd01DSWdjM1I1YkdVOUltSmhZMnRuY205MWJtUXRZMjlzYjNJNklDTm1abVk3SUdKdmNtUmxjaTF5WVdScGRYTTZJREp3ZUNJS0lDQnpkSEp2YTJVdGQybGtkR2c5SWpJaUNpQWdjM1J5YjJ0bExXeHBibVZqWVhBOUluSnZkVzVrSWdvZ0lITjBjbTlyWlMxc2FXNWxhbTlwYmowaWNtOTFibVFpQ2o0S0lDQThjR0YwYUNCa1BTSnROaUE1SURZZ05pQTJMVFlpSUM4K0Nqd3ZjM1puUGdvPSkgLSBodHRwczovL2x1Y2lkZS5kZXYvaWNvbnMvY2hldnJvbi1kb3duXG4gKiBAc2VlIGh0dHBzOi8vbHVjaWRlLmRldi9ndWlkZS9wYWNrYWdlcy9sdWNpZGUtcmVhY3QgLSBEb2N1bWVudGF0aW9uXG4gKlxuICogQHBhcmFtIHtPYmplY3R9IHByb3BzIC0gTHVjaWRlIGljb25zIHByb3BzIGFuZCBhbnkgdmFsaWQgU1ZHIGF0dHJpYnV0ZVxuICogQHJldHVybnMge0pTWC5FbGVtZW50fSBKU1ggRWxlbWVudFxuICpcbiAqL1xuY29uc3QgQ2hldnJvbkRvd24gPSBjcmVhdGVMdWNpZGVJY29uKCdjaGV2cm9uLWRvd24nLCBfX2ljb25Ob2RlKTtcblxuZXhwb3J0IGRlZmF1bHQgQ2hldnJvbkRvd247XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js":
/*!**************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/settings.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Settings)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z\",\n            key: \"1qme2f\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"3\",\n            key: \"1v7zrd\"\n        }\n    ]\n];\nconst Settings = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"settings\", __iconNode);\n //# sourceMappingURL=settings.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvc2V0dGluZ3MuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBR08sTUFBTSxVQUF1QjtJQUNsQztRQUNFO1FBQ0E7WUFDRSxDQUFHO1lBQ0gsR0FBSztRQUFBO0tBRVQ7SUFDQTtRQUFDLENBQVU7UUFBQTtZQUFFLENBQUksUUFBTTtZQUFBLEVBQUk7WUFBTSxDQUFHO1lBQUssQ0FBSztRQUFVO0tBQUE7Q0FDMUQ7QUFhTSxlQUFXLGtFQUFpQixhQUFZLENBQVUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcY2hpY2tcXHNyY1xcaWNvbnNcXHNldHRpbmdzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24nO1xuaW1wb3J0IHsgSWNvbk5vZGUgfSBmcm9tICcuLi90eXBlcyc7XG5cbmV4cG9ydCBjb25zdCBfX2ljb25Ob2RlOiBJY29uTm9kZSA9IFtcbiAgW1xuICAgICdwYXRoJyxcbiAgICB7XG4gICAgICBkOiAnTTEyLjIyIDJoLS40NGEyIDIgMCAwIDAtMiAydi4xOGEyIDIgMCAwIDEtMSAxLjczbC0uNDMuMjVhMiAyIDAgMCAxLTIgMGwtLjE1LS4wOGEyIDIgMCAwIDAtMi43My43M2wtLjIyLjM4YTIgMiAwIDAgMCAuNzMgMi43M2wuMTUuMWEyIDIgMCAwIDEgMSAxLjcydi41MWEyIDIgMCAwIDEtMSAxLjc0bC0uMTUuMDlhMiAyIDAgMCAwLS43MyAyLjczbC4yMi4zOGEyIDIgMCAwIDAgMi43My43M2wuMTUtLjA4YTIgMiAwIDAgMSAyIDBsLjQzLjI1YTIgMiAwIDAgMSAxIDEuNzNWMjBhMiAyIDAgMCAwIDIgMmguNDRhMiAyIDAgMCAwIDItMnYtLjE4YTIgMiAwIDAgMSAxLTEuNzNsLjQzLS4yNWEyIDIgMCAwIDEgMiAwbC4xNS4wOGEyIDIgMCAwIDAgMi43My0uNzNsLjIyLS4zOWEyIDIgMCAwIDAtLjczLTIuNzNsLS4xNS0uMDhhMiAyIDAgMCAxLTEtMS43NHYtLjVhMiAyIDAgMCAxIDEtMS43NGwuMTUtLjA5YTIgMiAwIDAgMCAuNzMtMi43M2wtLjIyLS4zOGEyIDIgMCAwIDAtMi43My0uNzNsLS4xNS4wOGEyIDIgMCAwIDEtMiAwbC0uNDMtLjI1YTIgMiAwIDAgMS0xLTEuNzNWNGEyIDIgMCAwIDAtMi0yeicsXG4gICAgICBrZXk6ICcxcW1lMmYnLFxuICAgIH0sXG4gIF0sXG4gIFsnY2lyY2xlJywgeyBjeDogJzEyJywgY3k6ICcxMicsIHI6ICczJywga2V5OiAnMXY3enJkJyB9XSxcbl07XG5cbi8qKlxuICogQGNvbXBvbmVudCBAbmFtZSBTZXR0aW5nc1xuICogQGRlc2NyaXB0aW9uIEx1Y2lkZSBTVkcgaWNvbiBjb21wb25lbnQsIHJlbmRlcnMgU1ZHIEVsZW1lbnQgd2l0aCBjaGlsZHJlbi5cbiAqXG4gKiBAcHJldmlldyAhW2ltZ10oZGF0YTppbWFnZS9zdmcreG1sO2Jhc2U2NCxQSE4yWnlBZ2VHMXNibk05SW1oMGRIQTZMeTkzZDNjdWR6TXViM0puTHpJd01EQXZjM1puSWdvZ0lIZHBaSFJvUFNJeU5DSUtJQ0JvWldsbmFIUTlJakkwSWdvZ0lIWnBaWGRDYjNnOUlqQWdNQ0F5TkNBeU5DSUtJQ0JtYVd4c1BTSnViMjVsSWdvZ0lITjBjbTlyWlQwaUl6QXdNQ0lnYzNSNWJHVTlJbUpoWTJ0bmNtOTFibVF0WTI5c2IzSTZJQ05tWm1ZN0lHSnZjbVJsY2kxeVlXUnBkWE02SURKd2VDSUtJQ0J6ZEhKdmEyVXRkMmxrZEdnOUlqSWlDaUFnYzNSeWIydGxMV3hwYm1WallYQTlJbkp2ZFc1a0lnb2dJSE4wY205clpTMXNhVzVsYW05cGJqMGljbTkxYm1RaUNqNEtJQ0E4Y0dGMGFDQmtQU0pOTVRJdU1qSWdNbWd0TGpRMFlUSWdNaUF3SURBZ01DMHlJREoyTGpFNFlUSWdNaUF3SURBZ01TMHhJREV1TnpOc0xTNDBNeTR5TldFeUlESWdNQ0F3SURFdE1pQXdiQzB1TVRVdExqQTRZVElnTWlBd0lEQWdNQzB5TGpjekxqY3piQzB1TWpJdU16aGhNaUF5SURBZ01DQXdJQzQzTXlBeUxqY3piQzR4TlM0eFlUSWdNaUF3SURBZ01TQXhJREV1TnpKMkxqVXhZVElnTWlBd0lEQWdNUzB4SURFdU56UnNMUzR4TlM0d09XRXlJRElnTUNBd0lEQXRMamN6SURJdU56TnNMakl5TGpNNFlUSWdNaUF3SURBZ01DQXlMamN6TGpjemJDNHhOUzB1TURoaE1pQXlJREFnTUNBeElESWdNR3d1TkRNdU1qVmhNaUF5SURBZ01DQXhJREVnTVM0M00xWXlNR0V5SURJZ01DQXdJREFnTWlBeWFDNDBOR0V5SURJZ01DQXdJREFnTWkweWRpMHVNVGhoTWlBeUlEQWdNQ0F4SURFdE1TNDNNMnd1TkRNdExqSTFZVElnTWlBd0lEQWdNU0F5SURCc0xqRTFMakE0WVRJZ01pQXdJREFnTUNBeUxqY3pMUzQzTTJ3dU1qSXRMak01WVRJZ01pQXdJREFnTUMwdU56TXRNaTQzTTJ3dExqRTFMUzR3T0dFeUlESWdNQ0F3SURFdE1TMHhMamMwZGkwdU5XRXlJRElnTUNBd0lERWdNUzB4TGpjMGJDNHhOUzB1TURsaE1pQXlJREFnTUNBd0lDNDNNeTB5TGpjemJDMHVNakl0TGpNNFlUSWdNaUF3SURBZ01DMHlMamN6TFM0M00yd3RMakUxTGpBNFlUSWdNaUF3SURBZ01TMHlJREJzTFM0ME15MHVNalZoTWlBeUlEQWdNQ0F4TFRFdE1TNDNNMVkwWVRJZ01pQXdJREFnTUMweUxUSjZJaUF2UGdvZ0lEeGphWEpqYkdVZ1kzZzlJakV5SWlCamVUMGlNVElpSUhJOUlqTWlJQzgrQ2p3dmMzWm5QZ289KSAtIGh0dHBzOi8vbHVjaWRlLmRldi9pY29ucy9zZXR0aW5nc1xuICogQHNlZSBodHRwczovL2x1Y2lkZS5kZXYvZ3VpZGUvcGFja2FnZXMvbHVjaWRlLXJlYWN0IC0gRG9jdW1lbnRhdGlvblxuICpcbiAqIEBwYXJhbSB7T2JqZWN0fSBwcm9wcyAtIEx1Y2lkZSBpY29ucyBwcm9wcyBhbmQgYW55IHZhbGlkIFNWRyBhdHRyaWJ1dGVcbiAqIEByZXR1cm5zIHtKU1guRWxlbWVudH0gSlNYIEVsZW1lbnRcbiAqXG4gKi9cbmNvbnN0IFNldHRpbmdzID0gY3JlYXRlTHVjaWRlSWNvbignc2V0dGluZ3MnLCBfX2ljb25Ob2RlKTtcblxuZXhwb3J0IGRlZmF1bHQgU2V0dGluZ3M7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/layout/AppLayout.tsx":
/*!*********************************************!*\
  !*** ./src/components/layout/AppLayout.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _pages_Dashboard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../pages/Dashboard */ \"(app-pages-browser)/./src/components/pages/Dashboard.tsx\");\n/* harmony import */ var _pages_Notes__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../pages/Notes */ \"(app-pages-browser)/./src/components/pages/Notes.tsx\");\n/* harmony import */ var _pages_Settings__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../pages/Settings */ \"(app-pages-browser)/./src/components/pages/Settings.tsx\");\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Header */ \"(app-pages-browser)/./src/components/layout/Header.tsx\");\n/* harmony import */ var _ui_AIAssistantChat__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../ui/AIAssistantChat */ \"(app-pages-browser)/./src/components/ui/AIAssistantChat.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst AppLayout = (param)=>{\n    let { children } = param;\n    _s();\n    const [activeSection, setActiveSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('dashboard');\n    const [showAIChat, setShowAIChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentLanguage, setCurrentLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('en');\n    // Load saved language preference\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppLayout.useEffect\": ()=>{\n            const savedLanguage = localStorage.getItem('koi-app-language');\n            if (savedLanguage) {\n                setCurrentLanguage(savedLanguage);\n            }\n        }\n    }[\"AppLayout.useEffect\"], []);\n    // Save language preference\n    const handleLanguageChange = (language)=>{\n        setCurrentLanguage(language);\n        localStorage.setItem('koi-app-language', language);\n    };\n    const handleAIAssistantClick = ()=>{\n        setShowAIChat(true);\n    };\n    const handleAccountClick = ()=>{\n        // Handle sign out\n        console.log('Sign out clicked');\n    // You can add actual sign out logic here\n    };\n    const handleSettingsClick = ()=>{\n        setActiveSection('settings');\n    };\n    const renderContent = ()=>{\n        switch(activeSection){\n            case 'dashboard':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_pages_Dashboard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 16\n                }, undefined);\n            case 'notes':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_pages_Notes__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 16\n                }, undefined);\n            case 'settings':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_pages_Settings__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    onBack: ()=>setActiveSection('dashboard'),\n                    currentLanguage: currentLanguage,\n                    onLanguageChange: handleLanguageChange\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 11\n                }, undefined);\n            case 'tasks':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-white\",\n                        children: \"Tasks - Coming Soon!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 51\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 16\n                }, undefined);\n            case 'content':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-white\",\n                        children: \"Content Creator - Coming Soon!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 51\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 16\n                }, undefined);\n            case 'calendar':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-white\",\n                        children: \"Calendar - Coming Soon!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 51\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-white\",\n                        children: [\n                            activeSection,\n                            \" - Coming Soon!\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 51\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                onAIAssistantClick: handleAIAssistantClick,\n                onAccountClick: handleAccountClick,\n                onSettingsClick: handleSettingsClick,\n                onLanguageChange: handleLanguageChange,\n                currentLanguage: currentLanguage\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: children || renderContent()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_AIAssistantChat__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                isOpen: showAIChat,\n                onClose: ()=>setShowAIChat(false),\n                language: currentLanguage\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\AppLayout.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AppLayout, \"uXcGXh1QDrNmucpMRGmSdNd8oq4=\");\n_c = AppLayout;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AppLayout);\nvar _c;\n$RefreshReg$(_c, \"AppLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/AppLayout.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Header.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Bot_ChevronDown_Globe_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,ChevronDown,Globe,Settings,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_ChevronDown_Globe_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,ChevronDown,Globe,Settings,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_ChevronDown_Globe_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,ChevronDown,Globe,Settings,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_ChevronDown_Globe_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,ChevronDown,Globe,Settings,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_ChevronDown_Globe_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,ChevronDown,Globe,Settings,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst Header = (param)=>{\n    let { onAIAssistantClick, onAccountClick, onSettingsClick, onLanguageChange, currentLanguage } = param;\n    _s();\n    const [showLanguageDropdown, setShowLanguageDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAccountDropdown, setShowAccountDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const languages = [\n        {\n            code: 'en',\n            label: 'US English',\n            flag: '🇺🇸'\n        },\n        {\n            code: 'es',\n            label: 'ES Español',\n            flag: '🇪🇸'\n        },\n        {\n            code: 'fr',\n            label: 'FR Français',\n            flag: '🇫🇷'\n        },\n        {\n            code: 'de',\n            label: 'DE Deutsch',\n            flag: '🇩🇪'\n        },\n        {\n            code: 'it',\n            label: 'IT Italiano',\n            flag: '🇮🇹'\n        },\n        {\n            code: 'pt',\n            label: 'PT Português',\n            flag: '🇵🇹'\n        },\n        {\n            code: 'nl',\n            label: 'NL Nederlands',\n            flag: '🇳🇱'\n        },\n        {\n            code: 'zh',\n            label: 'CN 中文',\n            flag: '🇨🇳'\n        },\n        {\n            code: 'ja',\n            label: 'JP 日本語',\n            flag: '🇯🇵'\n        },\n        {\n            code: 'ko',\n            label: 'KR 한국어',\n            flag: '🇰🇷'\n        },\n        {\n            code: 'ru',\n            label: 'RU Русский',\n            flag: '🇷🇺'\n        },\n        {\n            code: 'gr',\n            label: 'GR Ελληνικά',\n            flag: '🇬🇷'\n        },\n        {\n            code: 'he',\n            label: 'IL עברית',\n            flag: '🇮🇱'\n        },\n        {\n            code: 'th',\n            label: 'TH ไทย',\n            flag: '🇹🇭'\n        },\n        {\n            code: 'vi',\n            label: 'VN Tiếng Việt',\n            flag: '🇻🇳'\n        },\n        {\n            code: 'id',\n            label: 'ID Bahasa Indonesia',\n            flag: '🇮🇩'\n        }\n    ];\n    const currentLang = languages.find((lang)=>lang.code === currentLanguage) || languages[0];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-gradient-to-r from-blue-600 via-blue-700 to-blue-800 shadow-lg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-12 h-12 bg-gradient-to-r from-orange-400 to-orange-500 rounded-full flex items-center justify-center shadow-lg animate-bounce\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white font-bold text-xl\",\n                                        children: \"\\uD83D\\uDC20\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 53,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-xl font-bold text-white\",\n                                            children: \"Koi App\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 56,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-blue-200 text-sm\",\n                                            children: \"Your Ultimate AI Productivity Assistant\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onAIAssistantClick,\n                                    className: \"flex items-center space-x-2 bg-blue-500/80 hover:bg-blue-400/80 px-4 py-2 rounded-lg transition-colors text-white\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_ChevronDown_Globe_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium\",\n                                            children: \"AI Assistant\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowLanguageDropdown(!showLanguageDropdown),\n                                            className: \"flex items-center space-x-2 bg-blue-500/80 hover:bg-blue-400/80 px-4 py-2 rounded-lg transition-colors text-white\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_ChevronDown_Globe_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                    lineNumber: 82,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: currentLang.label\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                    lineNumber: 83,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_ChevronDown_Globe_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                    lineNumber: 84,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        showLanguageDropdown && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute right-0 mt-2 w-64 bg-blue-600 rounded-xl shadow-lg border border-blue-400/30 z-50 max-h-80 overflow-y-auto\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2\",\n                                                children: languages.map((language)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>{\n                                                            onLanguageChange(language.code);\n                                                            setShowLanguageDropdown(false);\n                                                        },\n                                                        className: \"w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors \".concat(currentLanguage === language.code ? 'bg-blue-400/50 text-white' : 'text-blue-200 hover:bg-blue-500/30 hover:text-white'),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-lg\",\n                                                                children: language.flag\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                                lineNumber: 103,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: language.label\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                                lineNumber: 104,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            currentLanguage === language.code && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"ml-auto text-blue-200\",\n                                                                children: \"✓\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                                lineNumber: 106,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, language.code, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                        lineNumber: 91,\n                                                        columnNumber: 23\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowAccountDropdown(!showAccountDropdown),\n                                            className: \"flex items-center space-x-2 bg-blue-500/80 hover:bg-blue-400/80 px-4 py-2 rounded-lg transition-colors text-white\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_ChevronDown_Globe_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                    lineNumber: 121,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Account\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                    lineNumber: 122,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        showAccountDropdown && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute right-0 mt-2 w-48 bg-blue-600 rounded-xl shadow-lg border border-blue-400/30 z-50\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>{\n                                                        onAccountClick();\n                                                        setShowAccountDropdown(false);\n                                                    },\n                                                    className: \"w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors text-blue-200 hover:bg-blue-500/30 hover:text-white\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium\",\n                                                        children: \"Sign Out\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                        lineNumber: 135,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                    lineNumber: 128,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onSettingsClick,\n                                    className: \"flex items-center space-x-2 bg-blue-500/80 hover:bg-blue-400/80 px-4 py-2 rounded-lg transition-colors text-white\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_ChevronDown_Globe_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, undefined),\n            (showLanguageDropdown || showAccountDropdown) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-40\",\n                onClick: ()=>{\n                    setShowLanguageDropdown(false);\n                    setShowAccountDropdown(false);\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                lineNumber: 155,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Header, \"4XJTbBxSpei3JczC1jFA9SAJZ+w=\");\n_c = Header;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Header);\nvar _c;\n$RefreshReg$(_c, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/Header.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/AIAssistantChat.tsx":
/*!***********************************************!*\
  !*** ./src/components/ui/AIAssistantChat.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Bot_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Send,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Send,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Send,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Send,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst AIAssistantChat = (param)=>{\n    let { isOpen, onClose, language } = param;\n    _s();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: '1',\n            text: 'Hello! I\\'m your AI assistant. How can I help you today?',\n            sender: 'ai',\n            timestamp: new Date()\n        }\n    ]);\n    const [inputText, setInputText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isTyping, setIsTyping] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleSendMessage = async ()=>{\n        if (!inputText.trim()) return;\n        const userMessage = {\n            id: Date.now().toString(),\n            text: inputText,\n            sender: 'user',\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setInputText('');\n        setIsTyping(true);\n        // Simulate AI response\n        setTimeout(()=>{\n            const aiResponse = {\n                id: (Date.now() + 1).toString(),\n                text: 'I understand you said: \"'.concat(inputText, \"\\\". I'm here to help you with your productivity tasks, answer questions, and provide assistance with the Koi app features. What would you like to know more about?\"),\n                sender: 'ai',\n                timestamp: new Date()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    aiResponse\n                ]);\n            setIsTyping(false);\n        }, 1500);\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            handleSendMessage();\n        }\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-gradient-to-br from-blue-600 to-blue-700 rounded-2xl shadow-2xl w-full max-w-md h-[600px] flex flex-col\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between p-4 border-b border-blue-400/30\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        className: \"w-5 h-5 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\ui\\\\AIAssistantChat.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\ui\\\\AIAssistantChat.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-white\",\n                                            children: \"AI Assistant\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\ui\\\\AIAssistantChat.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-blue-200 text-sm\",\n                                            children: \"Always here to help\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\ui\\\\AIAssistantChat.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\ui\\\\AIAssistantChat.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\ui\\\\AIAssistantChat.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"p-2 hover:bg-blue-500/30 rounded-lg transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"w-5 h-5 text-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\ui\\\\AIAssistantChat.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\ui\\\\AIAssistantChat.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\ui\\\\AIAssistantChat.tsx\",\n                    lineNumber: 71,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 overflow-y-auto p-4 space-y-4\",\n                    children: [\n                        messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex \".concat(message.sender === 'user' ? 'justify-end' : 'justify-start'),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-2 max-w-[80%] \".concat(message.sender === 'user' ? 'flex-row-reverse space-x-reverse' : ''),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 rounded-full flex items-center justify-center \".concat(message.sender === 'user' ? 'bg-blue-500' : 'bg-blue-400'),\n                                            children: message.sender === 'user' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-4 h-4 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\ui\\\\AIAssistantChat.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 21\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                className: \"w-4 h-4 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\ui\\\\AIAssistantChat.tsx\",\n                                                lineNumber: 103,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\ui\\\\AIAssistantChat.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"rounded-xl p-3 \".concat(message.sender === 'user' ? 'bg-blue-500 text-white' : 'bg-blue-800/50 text-blue-100'),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm\",\n                                                    children: message.text\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\ui\\\\AIAssistantChat.tsx\",\n                                                    lineNumber: 111,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs opacity-70 mt-1\",\n                                                    children: message.timestamp.toLocaleTimeString([], {\n                                                        hour: '2-digit',\n                                                        minute: '2-digit'\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\ui\\\\AIAssistantChat.tsx\",\n                                                    lineNumber: 112,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\ui\\\\AIAssistantChat.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\ui\\\\AIAssistantChat.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, message.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\ui\\\\AIAssistantChat.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 13\n                            }, undefined)),\n                        isTyping && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-start\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start space-x-2 max-w-[80%]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-blue-400 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            className: \"w-4 h-4 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\ui\\\\AIAssistantChat.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\ui\\\\AIAssistantChat.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-800/50 text-blue-100 rounded-xl p-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-blue-300 rounded-full animate-bounce\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\ui\\\\AIAssistantChat.tsx\",\n                                                    lineNumber: 128,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-blue-300 rounded-full animate-bounce\",\n                                                    style: {\n                                                        animationDelay: '0.1s'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\ui\\\\AIAssistantChat.tsx\",\n                                                    lineNumber: 129,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-blue-300 rounded-full animate-bounce\",\n                                                    style: {\n                                                        animationDelay: '0.2s'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\ui\\\\AIAssistantChat.tsx\",\n                                                    lineNumber: 130,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\ui\\\\AIAssistantChat.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\ui\\\\AIAssistantChat.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\ui\\\\AIAssistantChat.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\ui\\\\AIAssistantChat.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\ui\\\\AIAssistantChat.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 border-t border-blue-400/30\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: inputText,\n                                onChange: (e)=>setInputText(e.target.value),\n                                onKeyPress: handleKeyPress,\n                                placeholder: \"Type your message...\",\n                                className: \"flex-1 bg-blue-800/50 text-white placeholder-blue-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\ui\\\\AIAssistantChat.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleSendMessage,\n                                disabled: !inputText.trim(),\n                                className: \"bg-blue-500 hover:bg-blue-400 disabled:bg-blue-600 disabled:opacity-50 text-white p-2 rounded-lg transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\ui\\\\AIAssistantChat.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\ui\\\\AIAssistantChat.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\ui\\\\AIAssistantChat.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\ui\\\\AIAssistantChat.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\ui\\\\AIAssistantChat.tsx\",\n            lineNumber: 69,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\ui\\\\AIAssistantChat.tsx\",\n        lineNumber: 68,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AIAssistantChat, \"mBuQl4IMKiZSqsJt0gooNHUZyt0=\");\n_c = AIAssistantChat;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AIAssistantChat);\nvar _c;\n$RefreshReg$(_c, \"AIAssistantChat\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/AIAssistantChat.tsx\n"));

/***/ })

});