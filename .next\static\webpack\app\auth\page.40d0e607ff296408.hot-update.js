"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/page",{

/***/ "(app-pages-browser)/./src/components/pages/Auth.tsx":
/*!***************************************!*\
  !*** ./src/components/pages/Auth.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_i18n_useTranslation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../lib/i18n/useTranslation */ \"(app-pages-browser)/./src/lib/i18n/useTranslation.ts\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Eye_EyeOff_Globe_Lock_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Eye,EyeOff,Globe,Lock,Mail!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Eye_EyeOff_Globe_Lock_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Eye,EyeOff,Globe,Lock,Mail!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Eye_EyeOff_Globe_Lock_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Eye,EyeOff,Globe,Lock,Mail!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Eye_EyeOff_Globe_Lock_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Eye,EyeOff,Globe,Lock,Mail!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Eye_EyeOff_Globe_Lock_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Eye,EyeOff,Globe,Lock,Mail!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Eye_EyeOff_Globe_Lock_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Eye,EyeOff,Globe,Lock,Mail!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst Auth = (param)=>{\n    let { language = 'en', onLanguageChange } = param;\n    _s();\n    const { t } = (0,_lib_i18n_useTranslation__WEBPACK_IMPORTED_MODULE_3__.useTranslation)(language);\n    const { signUp, signIn, signInWithMagicLink, signInWithGoogle } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [authMode, setAuthMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('signin');\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [showLanguageDropdown, setShowLanguageDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const languages = [\n        {\n            code: 'en',\n            label: 'US English',\n            flag: '🇺🇸'\n        },\n        {\n            code: 'es',\n            label: 'ES Español',\n            flag: '🇪🇸'\n        },\n        {\n            code: 'fr',\n            label: 'FR Français',\n            flag: '🇫🇷'\n        },\n        {\n            code: 'de',\n            label: 'DE Deutsch',\n            flag: '🇩🇪'\n        },\n        {\n            code: 'it',\n            label: 'IT Italiano',\n            flag: '🇮🇹'\n        },\n        {\n            code: 'pt',\n            label: 'PT Português',\n            flag: '🇵🇹'\n        },\n        {\n            code: 'nl',\n            label: 'NL Nederlands',\n            flag: '🇳🇱'\n        },\n        {\n            code: 'zh',\n            label: 'CN 中文',\n            flag: '🇨🇳'\n        },\n        {\n            code: 'ja',\n            label: 'JP 日本語',\n            flag: '🇯🇵'\n        },\n        {\n            code: 'ko',\n            label: 'KR 한국어',\n            flag: '🇰🇷'\n        },\n        {\n            code: 'ru',\n            label: 'RU Русский',\n            flag: '🇷🇺'\n        },\n        {\n            code: 'gr',\n            label: 'GR Ελληνικά',\n            flag: '🇬🇷'\n        }\n    ];\n    const currentLang = languages.find((lang)=>lang.code === language) || languages[0];\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setLoading(true);\n        setMessage('');\n        try {\n            let result;\n            if (authMode === 'signup') {\n                result = await signUp(email, password);\n                if (!result.error) {\n                    setMessage('Check your email for verification link!');\n                }\n            } else if (authMode === 'signin') {\n                result = await signIn(email, password);\n            } else if (authMode === 'magic') {\n                result = await signInWithMagicLink(email);\n                if (!result.error) {\n                    setMessage('Check your email for the magic link!');\n                }\n            }\n            if (result === null || result === void 0 ? void 0 : result.error) {\n                setMessage(result.error.message);\n            }\n        } catch (error) {\n            setMessage(error.message);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleGoogleSignIn = async ()=>{\n        setLoading(true);\n        try {\n            const { error } = await signInWithGoogle();\n            if (error) {\n                setMessage(error.message);\n            }\n        } catch (error) {\n            setMessage(error.message);\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800 flex items-center justify-center p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(255,255,255,0.1),transparent_50%)]\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative w-full max-w-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative inline-block\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-20 h-20 bg-gradient-to-br from-cyan-300 via-blue-400 to-purple-500 rounded-full flex items-center justify-center shadow-2xl mx-auto mb-4 animate-bounce border-4 border-white/20\",\n                                    style: {\n                                        animationDuration: '3s'\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        width: \"48\",\n                                        height: \"48\",\n                                        viewBox: \"0 0 100 100\",\n                                        className: \"drop-shadow-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ellipse\", {\n                                                cx: \"40\",\n                                                cy: \"50\",\n                                                rx: \"25\",\n                                                ry: \"20\",\n                                                fill: \"url(#koiGradient)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M15 50 Q5 40 10 30 Q15 35 20 40 Q15 45 10 50 Q15 55 20 60 Q15 65 10 70 Q5 60 15 50\",\n                                                fill: \"url(#finGradient)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 107,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M35 30 Q30 20 40 25 Q45 30 35 30\",\n                                                fill: \"url(#finGradient)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 110,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ellipse\", {\n                                                cx: \"30\",\n                                                cy: \"60\",\n                                                rx: \"8\",\n                                                ry: \"4\",\n                                                fill: \"url(#finGradient)\",\n                                                transform: \"rotate(30 30 60)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 113,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ellipse\", {\n                                                cx: \"50\",\n                                                cy: \"60\",\n                                                rx: \"8\",\n                                                ry: \"4\",\n                                                fill: \"url(#finGradient)\",\n                                                transform: \"rotate(-30 50 60)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                cx: \"45\",\n                                                cy: \"45\",\n                                                r: \"4\",\n                                                fill: \"white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                cx: \"45\",\n                                                cy: \"45\",\n                                                r: \"2.5\",\n                                                fill: \"black\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                cx: \"46\",\n                                                cy: \"44\",\n                                                r: \"1\",\n                                                fill: \"white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                cx: \"35\",\n                                                cy: \"40\",\n                                                r: \"2\",\n                                                fill: \"#90EE90\",\n                                                opacity: \"0.8\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                cx: \"40\",\n                                                cy: \"35\",\n                                                r: \"1.5\",\n                                                fill: \"#90EE90\",\n                                                opacity: \"0.8\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                cx: \"30\",\n                                                cy: \"50\",\n                                                r: \"1.5\",\n                                                fill: \"#90EE90\",\n                                                opacity: \"0.8\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M55 50 Q60 52 55 54\",\n                                                stroke: \"black\",\n                                                strokeWidth: \"1\",\n                                                fill: \"none\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                                                        id: \"koiGradient\",\n                                                        x1: \"0%\",\n                                                        y1: \"0%\",\n                                                        x2: \"100%\",\n                                                        y2: \"100%\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                offset: \"0%\",\n                                                                stopColor: \"#00FFFF\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                                lineNumber: 132,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                offset: \"50%\",\n                                                                stopColor: \"#0080FF\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                                lineNumber: 133,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                offset: \"100%\",\n                                                                stopColor: \"#8000FF\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                                lineNumber: 134,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                        lineNumber: 131,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                                                        id: \"finGradient\",\n                                                        x1: \"0%\",\n                                                        y1: \"0%\",\n                                                        x2: \"100%\",\n                                                        y2: \"100%\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                offset: \"0%\",\n                                                                stopColor: \"#FF69B4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                                lineNumber: 137,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                offset: \"50%\",\n                                                                stopColor: \"#DA70D6\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                                lineNumber: 138,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                offset: \"100%\",\n                                                                stopColor: \"#9370DB\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                                lineNumber: 139,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                        lineNumber: 136,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-white mb-1\",\n                                children: \"Welcome to Koi App\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-200 text-base\",\n                                children: \"Your personal productivity assistant\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-br from-blue-500/80 to-blue-600/80 backdrop-blur-lg rounded-3xl p-6 shadow-2xl border border-blue-400/30\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowLanguageDropdown(!showLanguageDropdown),\n                                        className: \"w-full flex items-center justify-center space-x-2 bg-blue-400/30 hover:bg-blue-400/50 px-3 py-2.5 rounded-xl transition-colors text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Eye_EyeOff_Globe_Lock_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium\",\n                                                children: [\n                                                    currentLang.flag,\n                                                    \" \",\n                                                    currentLang.label\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Eye_EyeOff_Globe_Lock_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    showLanguageDropdown && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-full left-0 right-0 mt-2 bg-blue-600 rounded-xl shadow-lg border border-blue-400/30 z-50 max-h-60 overflow-y-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-2\",\n                                            children: languages.map((lang)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>{\n                                                        onLanguageChange === null || onLanguageChange === void 0 ? void 0 : onLanguageChange(lang.code);\n                                                        setShowLanguageDropdown(false);\n                                                    },\n                                                    className: \"w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors \".concat(language === lang.code ? 'bg-blue-400/50 text-white' : 'text-blue-200 hover:bg-blue-500/30 hover:text-white'),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-lg\",\n                                                            children: lang.flag\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                            lineNumber: 183,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: lang.label\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                            lineNumber: 184,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, lang.code, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex bg-blue-600/50 rounded-2xl p-1 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setAuthMode('signin'),\n                                        className: \"flex-1 py-2.5 px-3 rounded-xl text-sm font-semibold transition-all \".concat(authMode === 'signin' ? 'bg-blue-400 text-white shadow-lg' : 'text-blue-200 hover:text-white'),\n                                        children: \"Sign In\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setAuthMode('signup'),\n                                        className: \"flex-1 py-2.5 px-3 rounded-xl text-sm font-semibold transition-all \".concat(authMode === 'signup' ? 'bg-blue-400 text-white shadow-lg' : 'text-blue-200 hover:text-white'),\n                                        children: \"Sign Up\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setAuthMode('magic'),\n                                        className: \"flex-1 py-2.5 px-3 rounded-xl text-sm font-semibold transition-all \".concat(authMode === 'magic' ? 'bg-blue-400 text-white shadow-lg' : 'text-blue-200 hover:text-white'),\n                                        children: \"Magic Link\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmit,\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-blue-200 text-sm font-medium mb-1.5\",\n                                                children: \"Email\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Eye_EyeOff_Globe_Lock_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-blue-300\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                        lineNumber: 234,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"email\",\n                                                        value: email,\n                                                        onChange: (e)=>setEmail(e.target.value),\n                                                        placeholder: \"Enter your email\",\n                                                        className: \"w-full bg-blue-700/50 text-white placeholder-blue-300 rounded-xl pl-10 pr-4 py-3 focus:outline-none focus:ring-2 focus:ring-cyan-400 border border-blue-500/30\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    authMode !== 'magic' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center mb-1.5\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-blue-200 text-sm font-medium\",\n                                                        children: \"Password\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                        lineNumber: 250,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    authMode === 'signin' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        className: \"text-blue-300 text-xs hover:text-white transition-colors\",\n                                                        children: \"Forgot password?\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                        lineNumber: 254,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Eye_EyeOff_Globe_Lock_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-blue-300\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: showPassword ? 'text' : 'password',\n                                                        value: password,\n                                                        onChange: (e)=>setPassword(e.target.value),\n                                                        placeholder: \"••••••••\",\n                                                        className: \"w-full bg-blue-700/50 text-white placeholder-blue-300 rounded-xl pl-10 pr-10 py-3 focus:outline-none focus:ring-2 focus:ring-cyan-400 border border-blue-500/30\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: ()=>setShowPassword(!showPassword),\n                                                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-blue-300 hover:text-white transition-colors\",\n                                                        children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Eye_EyeOff_Globe_Lock_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                            lineNumber: 277,\n                                                            columnNumber: 37\n                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Eye_EyeOff_Globe_Lock_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                            lineNumber: 277,\n                                                            columnNumber: 70\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                        lineNumber: 272,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: loading,\n                                        className: \"w-full bg-gradient-to-r from-cyan-400 to-blue-500 hover:from-cyan-300 hover:to-blue-400 text-white font-semibold py-3 rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2\",\n                                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 17\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: authMode === 'signin' ? 'Sign In' : authMode === 'signup' ? 'Sign Up' : 'Send Magic Link'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                    lineNumber: 293,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"→\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 flex items-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full border-t border-blue-400/30\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative flex justify-center text-sm\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"px-2 bg-blue-600/80 text-blue-200\",\n                                                    children: \"or\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleGoogleSignIn,\n                                        disabled: loading,\n                                        className: \"w-full mt-3 bg-white hover:bg-gray-50 text-gray-900 font-semibold py-3 rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fill: \"#4285F4\",\n                                                        d: \"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                        lineNumber: 321,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fill: \"#34A853\",\n                                                        d: \"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                        lineNumber: 322,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fill: \"#FBBC05\",\n                                                        d: \"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                        lineNumber: 323,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        fill: \"#EA4335\",\n                                                        d: \"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                        lineNumber: 324,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Continue with Google\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 11\n                            }, undefined),\n                            message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 p-3 rounded-xl text-sm \".concat(message.includes('Check your email') ? 'bg-green-500/20 text-green-200 border border-green-400/30' : 'bg-red-500/20 text-red-200 border border-red-400/30'),\n                                children: message\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, undefined),\n            showLanguageDropdown && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-40\",\n                onClick: ()=>setShowLanguageDropdown(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n                lineNumber: 345,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\koi-app3\\\\src\\\\components\\\\pages\\\\Auth.tsx\",\n        lineNumber: 89,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Auth, \"UjxC3D3aqiHGTxaSKNO3bpH+I80=\", false, function() {\n    return [\n        _lib_i18n_useTranslation__WEBPACK_IMPORTED_MODULE_3__.useTranslation,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = Auth;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Auth);\nvar _c;\n$RefreshReg$(_c, \"Auth\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/pages/Auth.tsx\n"));

/***/ })

});